"""
Multi Agent Server for Multi Agents System
Serves all dynamic agents through A2A protocol on different ports
"""

import asyncio
import logging
from typing import Dict, List
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from ..agents.dynamic_agent_factory import DynamicAgentFactory, DynamicA2AAgent
from ..agents.types import AgentCard
from ..agents.config import A2AConfig


class MultiAgentServer:
    """
    Server để serve tất cả dynamic agents qua A2A protocol
    Mỗi agent chạy trên port riêng biệt
    """
    
    def __init__(self):
        self.logger = logging.getLogger("MultiAgentServer")
        self.factory = DynamicAgentFactory()
        self.agents: Dict[str, DynamicA2AAgent] = {}
        self.servers: Dict[str, uvicorn.Server] = {}
        
    async def initialize(self):
        """Initialize tất cả agents từ database"""
        self.logger.info("🚀 Initializing Multi Agent Server...")
        
        # <PERSON><PERSON> tất cả available agents từ database
        available_agents = self.factory.get_available_agents()
        self.logger.info(f"📋 Found {len(available_agents)} agents in database: {available_agents}")
        
        # Tạo tất cả agents
        for agent_name in available_agents:
            try:
                agent = self.factory.create_agent_from_db(agent_name)
                if agent:
                    self.agents[agent_name] = agent
                    self.logger.info(f"✅ Loaded agent: {agent_name} (port: {agent.port})")
                else:
                    self.logger.error(f"❌ Failed to load agent: {agent_name}")
            except Exception as e:
                self.logger.error(f"❌ Error loading agent {agent_name}: {str(e)}")
        
        self.logger.info(f"🎯 Successfully loaded {len(self.agents)} agents")
    
    def create_agent_app(self, agent: DynamicA2AAgent) -> FastAPI:
        """Tạo FastAPI app cho một agent"""
        app = FastAPI(
            title=f"{agent.agent_name} A2A Agent",
            description=f"A2A Protocol server for {agent.agent_name} agent",
            version="1.0.0"
        )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Agent Card endpoint
        @app.get("/.well-known/agent.json", response_model=AgentCard)
        async def get_agent_card():
            """Get agent card for A2A protocol"""
            return agent.get_agent_card()
        
        # Health check endpoint
        @app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "agent": agent.agent_name,
                "type": agent.agent_type,
                "port": agent.port
            }
        
        # A2A Task endpoint
        @app.post("/a2a/task")
        async def handle_a2a_task(request: dict):
            """Handle A2A task request"""
            try:
                task_content = request.get("content", "")
                session_id = request.get("session_id")
                
                result = await agent.handle_a2a_task(task_content, session_id)
                
                return {
                    "status": "success",
                    "result": result,
                    "agent": agent.agent_name
                }
            except Exception as e:
                self.logger.error(f"Error in A2A task for {agent.agent_name}: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Agent info endpoint
        @app.get("/info")
        async def get_agent_info():
            """Get detailed agent information"""
            agent_card = agent.get_agent_card()
            return {
                "name": agent.agent_name,
                "type": agent.agent_type,
                "port": agent.port,
                "url": agent_card.url,
                "skills": [skill.name for skill in agent_card.skills],
                "capabilities": agent_card.capabilities.model_dump(),
                "description": agent_card.description
            }
        
        return app
    
    async def start_agent_server(self, agent: DynamicA2AAgent):
        """Start server cho một agent"""
        app = self.create_agent_app(agent)
        
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=agent.port,
            log_level="info",
            access_log=False
        )
        
        server = uvicorn.Server(config)
        self.servers[agent.agent_name] = server
        
        self.logger.info(f"🌐 Starting server for {agent.agent_name} on port {agent.port}")
        await server.serve()
    
    async def start_all_servers(self):
        """Start tất cả agent servers"""
        self.logger.info("🚀 Starting all agent servers...")
        
        # Tạo tasks cho tất cả servers
        tasks = []
        for agent_name, agent in self.agents.items():
            task = asyncio.create_task(
                self.start_agent_server(agent),
                name=f"server-{agent_name}"
            )
            tasks.append(task)
        
        self.logger.info(f"🌐 Started {len(tasks)} agent servers")
        
        # Wait for all servers
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            self.logger.info("🛑 Shutting down servers...")
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown tất cả servers"""
        self.logger.info("🛑 Shutting down Multi Agent Server...")
        
        for agent_name, server in self.servers.items():
            self.logger.info(f"🛑 Stopping server for {agent_name}")
            server.should_exit = True
        
        self.logger.info("✅ All servers stopped")
    
    def get_agents_summary(self) -> List[Dict]:
        """Get summary của tất cả agents"""
        summary = []
        for agent_name, agent in self.agents.items():
            agent_card = agent.get_agent_card()
            summary.append({
                "name": agent_name,
                "type": agent.agent_type,
                "port": agent.port,
                "url": agent_card.url,
                "skills_count": len(agent_card.skills),
                "status": "running"
            })
        return summary


async def main():
    """Main function để chạy Multi Agent Server"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger("MultiAgentServer")
    
    try:
        # Tạo và initialize server
        server = MultiAgentServer()
        await server.initialize()
        
        # Print summary
        summary = server.get_agents_summary()
        logger.info("📊 Multi Agent Server Summary:")
        for agent_info in summary:
            logger.info(f"  🔧 {agent_info['name']}: {agent_info['url']} ({agent_info['skills_count']} skills)")
        
        # Start tất cả servers
        await server.start_all_servers()
        
    except Exception as e:
        logger.error(f"❌ Failed to start Multi Agent Server: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
