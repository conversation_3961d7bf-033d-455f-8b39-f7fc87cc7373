# MCP Server
## How MCP Server works?

The Model Context Protocol (MCP) operates using a **client-server architecture**. In this model:

*   **Servers** act as **providers of capabilities**, exposing **tools**, APIs, or data sources to clients. These servers can offer various functionalities. For example, the sources mention MCP servers for accessing the **file system, web search, databases, CRMs, and version control**. There are also specific examples like a **simple MCP weather server** and a **mcp-flight-search** server.
*   **Clients** are typically **AI applications or Large Language Models (LLMs)** that **connect to the MCP servers** to utilize the exposed tools.

This setup enables **dynamic and structured interactions** between LLM models and external APIs. MCP transforms the API into a **model-friendly tool**, complete with **auto-discovery** and a **predictable schema**.

Here's the typical workflow:

1.  **Server Initialization:** Developers build MCP servers using SDKs. For example, in Python, the `FastMCP` class is used to initialize a server.
2.  **Transport Mechanism:** MCP servers and clients communicate through a transport layer. A common method demonstrated is **stdio (standard input/output)**. This allows for language-neutral communication.
3.  **Tool Exposure and Discovery:** Once a client connects, the MCP server **registers its available tools**. The client can then **discover and list these tools**, along with their names, descriptions, and input schemas. This makes the MCP server **self-describing**.
4.  **Tool Invocation:** When an LLM in the client determines that a tool is needed, the client makes a **call to the MCP server**, specifying the **tool's name** and providing the required **arguments** based on the tool's schema.
5.  **Tool Execution:** Upon receiving a tool call, the **MCP server executes the logic** associated with that tool. For instance, the `mcp-flight-search` server uses the SerpAPI to fetch flight data.
6.  **Response Handling:** After execution, the **MCP server sends the result back to the client** in a structured format.

The Agent Development Kit (ADK) can act as an **MCP client** using the **MCPToolset**. The `MCPToolset` enables ADK agents to **connect with external MCP servers**, **discover available tools**, and **invoke them**. The **StdioServerParameters** class is used to specify how the ADK agent connects to an MCP server via standard input/output streams. When combined, `MCPToolset` and `StdioServerParameters` allow an ADK agent to **establish a connection**, **discover available tools**, **integrate tools into the agent**, and **manage the connection lifecycle**.

Key benefits of using MCP include **standardized integration, flexibility, security, simplified development, and easier maintenance**. MCP is particularly useful when building **agentic systems** and when tools need to be **modular, reusable, and discoverable**.


## How to apply in chatbots?

