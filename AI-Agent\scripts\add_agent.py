#!/usr/bin/env python3
"""
Auto-Scaling Agent Management Script
Allows adding new agents to the system without rebuilding Docker
"""

import psycopg2
import sys
import os
from typing import Dict, Any

class AgentManager:
    def __init__(self):
        # Check if running inside Docker container
        if os.path.exists('/.dockerenv'):
            # Inside Docker - use service name
            default_host = 'postgres'
        else:
            # Outside Docker - use localhost
            default_host = 'localhost'

        self.db_config = {
            'host': os.getenv('DB_HOST', default_host),
            'database': os.getenv('DB_NAME', 'ai_agent_db'),
            'user': os.getenv('DB_USER', 'ai_agent_user'),
            'password': os.getenv('DB_PASSWORD', 'ai_agent_password'),
            'port': os.getenv('DB_PORT', '5432')
        }
    
    def connect_db(self):
        """Connect to PostgreSQL database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return None
    
    def get_next_port(self, conn):
        """Get next available port for new agent"""
        cursor = conn.cursor()
        cursor.execute("SELECT MAX(port) FROM agents WHERE port IS NOT NULL")
        max_port = cursor.fetchone()[0]
        return (max_port + 1) if max_port else 5001
    
    def add_agent(self, agent_data: Dict[str, Any]):
        """Add new agent to database"""
        conn = self.connect_db()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Get next available port
            port = agent_data.get('port') or self.get_next_port(conn)
            
            # Insert agent
            insert_query = """
                INSERT INTO agents (name, role, goal, backstory, port, agent_type, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (
                agent_data['name'],
                agent_data['role'],
                agent_data['goal'],
                agent_data['backstory'],
                port,
                agent_data.get('agent_type', 'general'),
                agent_data.get('status', 'active')
            ))
            
            # Add corresponding task if provided
            if 'task' in agent_data:
                task_query = """
                    INSERT INTO tasks (name, description, expected_output, agent_name)
                    VALUES (%s, %s, %s, %s)
                """
                cursor.execute(task_query, (
                    f"{agent_data['name']}_task",
                    agent_data['task']['description'],
                    agent_data['task']['expected_output'],
                    agent_data['name']
                ))
            
            conn.commit()
            print(f"✅ Agent '{agent_data['name']}' added successfully on port {port}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to add agent: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def list_agents(self):
        """List all agents in database"""
        conn = self.connect_db()
        if not conn:
            return
        
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name, role, agent_type, port, status 
                FROM agents 
                ORDER BY port
            """)
            agents = cursor.fetchall()
            
            print("\n📋 Current Agents:")
            print("-" * 80)
            print(f"{'Name':<15} {'Role':<25} {'Type':<12} {'Port':<6} {'Status':<8}")
            print("-" * 80)
            
            for agent in agents:
                name, role, agent_type, port, status = agent
                # Remove {topic} placeholder for display
                display_role = role.replace('{topic} ', '')
                print(f"{name:<15} {display_role:<25} {agent_type:<12} {port:<6} {status:<8}")
            
            print(f"\nTotal: {len(agents)} agents")
            
        except Exception as e:
            print(f"❌ Failed to list agents: {e}")
        finally:
            conn.close()
    
    def remove_agent(self, agent_name: str):
        """Remove agent from database"""
        conn = self.connect_db()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Remove tasks first (foreign key constraint)
            cursor.execute("DELETE FROM tasks WHERE agent_name = %s", (agent_name,))
            
            # Remove agent
            cursor.execute("DELETE FROM agents WHERE name = %s", (agent_name,))
            
            if cursor.rowcount > 0:
                conn.commit()
                print(f"✅ Agent '{agent_name}' removed successfully")
                return True
            else:
                print(f"❌ Agent '{agent_name}' not found")
                return False
                
        except Exception as e:
            print(f"❌ Failed to remove agent: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

def main():
    manager = AgentManager()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python add_agent.py list                    - List all agents")
        print("  python add_agent.py add <name> <role> <goal> <backstory> [type]")
        print("  python add_agent.py remove <name>           - Remove agent")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        manager.list_agents()
    
    elif command == "add":
        if len(sys.argv) < 6:
            print("❌ Missing arguments for add command")
            print("Usage: python add_agent.py add <name> <role> <goal> <backstory> [type]")
            return
        
        agent_data = {
            'name': sys.argv[2],
            'role': sys.argv[3],
            'goal': sys.argv[4],
            'backstory': sys.argv[5],
            'agent_type': sys.argv[6] if len(sys.argv) > 6 else 'general'
        }
        
        manager.add_agent(agent_data)
    
    elif command == "remove":
        if len(sys.argv) < 3:
            print("❌ Missing agent name for remove command")
            return
        
        manager.remove_agent(sys.argv[2])
    
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()
