"""
MEM0 - Simple memory client for chatbots
Wrapper around the official mem0 library
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union
from dotenv import load_dotenv
from mem0 import MemoryClient as Mem0Client

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mem0")

class MemoryClient:
    """
    Memory client that wraps the official mem0 library
    Falls back to in-memory storage if API is not available
    """
    
    def __init__(self, api_key=None):
        """Initialize memory client"""
        self.api_key = api_key or os.getenv("MEM0_API_KEY", "")
        
        # In-memory fallback storage
        self._memory_storage = {}
        self._memory_ids = {}  # To track memory IDs for update/delete operations
        
        # Try to initialize official client
        self.use_api = bool(self.api_key)
        self.api_available = False
        
        if self.use_api:
            try:
                # Initialize official client
                logger.info("Initializing official mem0 client...")
                self.client = Mem0Client(api_key=self.api_key)
                self.api_available = True
                logger.info("Successfully initialized mem0 client")
            except Exception as e:
                logger.warning(f"Failed to initialize mem0 client: {str(e)}. Using in-memory fallback.")
                logger.debug(f"Full error: {repr(e)}")
        else:
            logger.warning("No API key provided. Using in-memory storage.")
            
    def add(self, messages: List[Dict], user_id: str = "default", 
            metadata: Dict = None, run_id: str = None, categories: List[str] = None):
        """
        Add messages to memory for a user
        Tries mem0 API first, falls back to in-memory storage
        
        Args:
            messages: List of message objects with 'role' and 'content'
            user_id: User identifier for this conversation
            metadata: Optional metadata to associate with the memory
            run_id: Optional run_id for short-term memory sessions
            categories: Optional categories for organizing memories
        
        Returns:
            Dict containing result information including memory_id
        """
        # Generate a local memory ID for in-memory storage
        memory_id = f"memory_{user_id}_{len(self._memory_storage.get(user_id, []))}"
        
        # Store memory data structure
        memory_data = {
            "messages": messages,
            "metadata": metadata or {},
            "run_id": run_id,
            "categories": categories or [],
            "timestamp": str(import_time()) if import_time else "fallback_timestamp"
        }
        
        # Always maintain in-memory copy for backup
        if user_id not in self._memory_storage:
            self._memory_storage[user_id] = []
        self._memory_storage[user_id].append(memory_data)
        self._memory_ids[memory_id] = {"user_id": user_id, "index": len(self._memory_storage[user_id])-1}
        
        # If API is not available, just use in-memory storage
        if not self.use_api or not self.api_available:
            logger.info(f"Added {len(messages)} messages for user {user_id} (in-memory)")
            return {"success": True, "storage": "in-memory", "memory_id": memory_id}
            
        try:
            # Use official client to add memory
            kwargs = {"user_id": user_id}
            if metadata:
                kwargs["metadata"] = metadata
            if run_id:
                kwargs["run_id"] = run_id
            if categories:
                kwargs["categories"] = categories
                
            result = self.client.add(messages, **kwargs)
            logger.info(f"Added {len(messages)} messages for user {user_id} (API)")
            
            # Store the real memory_id for future reference if available in result
            if isinstance(result, dict) and "memory_id" in result:
                self._memory_ids[result["memory_id"]] = {"user_id": user_id, "api": True}
                return {"success": True, "storage": "api", "memory_id": result["memory_id"]}
            return {"success": True, "storage": "api", "result": result}
            
        except Exception as e:
            logger.error(f"Error adding messages: {e}")
            self.api_available = False
            return {"success": False, "error": str(e), "storage": "in-memory", "memory_id": memory_id}
    
    def get_all(self, user_id: str = None, run_id: str = None, 
                categories: List[str] = None, metadata: Dict = None,
                page: int = 1, page_size: int = 50):
        """
        Get all memories matching the specified filters
        
        Args:
            user_id: Optional user ID filter
            run_id: Optional run ID for session-specific memories
            categories: Optional categories filter
            metadata: Optional metadata filter
            page: Page number (starting from 1)
            page_size: Number of items per page
            
        Returns:
            List of memories
        """
        # If API is not available, use in-memory storage
        if not self.use_api or not self.api_available:
            # Filter memories
            all_memories = []
            
            # If user_id is specified, only look at that user's memories
            if user_id:
                user_memories = self._memory_storage.get(user_id, [])
                
                # Apply filters
                filtered_memories = user_memories
                if run_id:
                    filtered_memories = [m for m in filtered_memories if m.get("run_id") == run_id]
                if categories:
                    filtered_memories = [m for m in filtered_memories 
                                         if any(c in m.get("categories", []) for c in categories)]
                if metadata:
                    filtered_memories = [m for m in filtered_memories 
                                         if all(m.get("metadata", {}).get(k) == v for k, v in metadata.items())]
                
                # Calculate pagination
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_memories = filtered_memories[start_idx:end_idx]
                
                # Format for return
                for idx, memory in enumerate(paginated_memories):
                    local_id = f"memory_{user_id}_{idx}"
                    all_memories.append({
                        "memory_id": local_id,
                        "messages": memory["messages"],
                        "metadata": memory.get("metadata", {}),
                        "run_id": memory.get("run_id"),
                        "categories": memory.get("categories", []),
                        "timestamp": memory.get("timestamp", "unknown")
                    })
            else:
                # Get memories for all users (but respect pagination across all users)
                all_user_memories = []
                for uid, memories in self._memory_storage.items():
                    # Apply filters
                    filtered_memories = memories
                    if run_id:
                        filtered_memories = [m for m in filtered_memories if m.get("run_id") == run_id]
                    if categories:
                        filtered_memories = [m for m in filtered_memories 
                                             if any(c in m.get("categories", []) for c in categories)]
                    if metadata:
                        filtered_memories = [m for m in filtered_memories 
                                             if all(m.get("metadata", {}).get(k) == v for k, v in metadata.items())]
                    
                    for idx, memory in enumerate(filtered_memories):
                        local_id = f"memory_{uid}_{idx}"
                        all_user_memories.append({
                            "memory_id": local_id,
                            "messages": memory["messages"],
                            "metadata": memory.get("metadata", {}),
                            "run_id": memory.get("run_id"),
                            "categories": memory.get("categories", []),
                            "timestamp": memory.get("timestamp", "unknown"),
                            "user_id": uid
                        })
                
                # Calculate pagination
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                all_memories = all_user_memories[start_idx:end_idx]
            
            logger.info(f"Found {len(all_memories)} memories (in-memory)")
            return all_memories
            
        try:
            # Build filters for the API call
            filters = {}
            and_filters = []
            
            if user_id:
                and_filters.append({"user_id": user_id})
            if run_id:
                and_filters.append({"run_id": run_id})
            
            # Add metadata filters if provided
            if metadata:
                for key, value in metadata.items():
                    and_filters.append({f"metadata.{key}": value})
            
            # Add category filters if provided
            if categories and len(categories) > 0:
                or_filters = [{"category": category} for category in categories]
                and_filters.append({"OR": or_filters})
            
            # Combine filters if we have any
            if and_filters:
                filters = {"AND": and_filters}
            
            # Use the official client get_all method
            results = self.client.get_all(
                version="v2", 
                filters=filters if filters else None,
                page=page,
                page_size=page_size
            )
            
            memory_count = len(results)
            logger.info(f"Found {memory_count} memories via get_all (API)")
            return results
                
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            # or just a minor error we can ignore
            error_str = str(e)
            
            # Non-critical errors that should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            logger.error(f"Error getting all memories: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                # Fall back to in-memory
                return self.get_all(user_id=user_id, run_id=run_id, 
                                   categories=categories, metadata=metadata,
                                   page=page, page_size=page_size)
            else:
                logger.info("Non-critical error, continuing with API mode")
                # Return empty list for non-critical errors
                return []
        
    def search(self, query: str, user_id: str = None, limit: int = 5, 
               categories: List[str] = None, metadata: Dict = None):
        """
        Search for relevant memories
        Tries mem0 API first, falls back to in-memory storage
        
        Args:
            query: Query to search for
            user_id: Optional user ID filter
            limit: Maximum number of results
            categories: Optional categories filter
            metadata: Optional metadata filter
            
        Returns:
            List of memories
        """
        # If query is empty, substitute with a general term
        if not query or not query.strip():
            query = "conversation history"
            logger.warning("Empty search query replaced with 'conversation history'")
            
        # If API is not available, use in-memory storage
        if not self.use_api or not self.api_available:
            # We'll return most recent memories as a simple "search"
            if user_id:
                user_memories = self._memory_storage.get(user_id, [])
            else:
                # Combine all users' memories
                user_memories = []
                for uid, memories in self._memory_storage.items():
                    for memory in memories:
                        memory_copy = memory.copy()
                        memory_copy["user_id"] = uid
                        user_memories.append(memory_copy)
            
            # Apply filters
            filtered_memories = user_memories
            if categories:
                filtered_memories = [m for m in filtered_memories 
                                     if any(c in m.get("categories", []) for c in categories)]
            if metadata:
                filtered_memories = [m for m in filtered_memories 
                                     if all(m.get("metadata", {}).get(k) == v for k, v in metadata.items())]
            
            # Sort by recency (assuming timestamps, which we don't really have in fallback)
            # So just return the most recent up to limit
            recent_memories = filtered_memories[-limit:]
            
            # Format for return
            memories = []
            for memory in recent_memories:
                uid = memory.get("user_id", user_id or "default")
                idx = filtered_memories.index(memory)
                memories.append({
                    "memory_id": f"memory_{uid}_{idx}",
                    "memory": {
                        "messages": memory["messages"],
                        "metadata": memory.get("metadata", {}),
                        "run_id": memory.get("run_id"),
                        "categories": memory.get("categories", [])
                    },
                    "score": 1.0  # Dummy score
                })
                
            logger.info(f"Found {len(memories)} memories (in-memory)")
            return memories
            
        try:
            # Build kwargs for the search call
            kwargs = {"limit": limit, "output_format": "v1.1"}
            if user_id:
                kwargs["user_id"] = user_id
            if categories:
                kwargs["categories"] = categories
            if metadata:
                kwargs["metadata"] = metadata
            
            # Use official client to search memories
            results = self.client.search(query, **kwargs)
            logger.info(f"Found {len(results)} memories for search: '{query}' (API)")
            
            # Safely log first result or an empty message if no results
            if results:
                logger.debug(f"First search result: {str(results[0])[:100]}...")
            else:
                logger.debug("No search results found")
            return results
            
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            # or just a minor error we can ignore
            error_str = str(e)
            
            # Indexing errors (like "0") or empty result errors should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            # Log the error but don't change API availability for non-critical errors
            logger.error(f"Error searching memories: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                # Fall back to in-memory search
                return self.search(query, user_id, limit, categories, metadata)
            else:
                logger.info("Non-critical error, continuing with API mode")
                # Return empty list for non-critical errors
                return []
    
    def history(self, memory_id: str):
        """
        Get history of how memory changed over time
        
        Args:
            memory_id: ID of the memory to get history for
            
        Returns:
            List of memory versions
        """
        if not self.use_api or not self.api_available:
            # In-memory fallback doesn't track history
            logger.warning("Memory history not available in fallback mode")
            return []
        
        try:
            result = self.client.history(memory_id)
            logger.info(f"Retrieved history for memory {memory_id} (API)")
            return result
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            error_str = str(e)
            
            # Non-critical errors that should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            logger.error(f"Error retrieving memory history: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                return []
            else:
                logger.info("Non-critical error, continuing with API mode")
                return []
    
    def update(self, memory_id: str, text: str = None, metadata: Dict = None):
        """
        Update a memory with new text or metadata
        
        Args:
            memory_id: ID of the memory to update
            text: New text content (optional)
            metadata: New metadata (optional)
            
        Returns:
            Dict with operation result
        """
        if not self.use_api or not self.api_available:
            # Try to update in-memory
            if memory_id in self._memory_ids:
                memory_info = self._memory_ids[memory_id]
                user_id = memory_info["user_id"]
                index = memory_info.get("index")
                
                if user_id in self._memory_storage and index is not None and index < len(self._memory_storage[user_id]):
                    memory = self._memory_storage[user_id][index]
                    
                    if text and memory.get("messages"):
                        # Update the last message content
                        memory["messages"][-1]["content"] = text
                    
                    if metadata:
                        # Update or set metadata
                        if "metadata" not in memory:
                            memory["metadata"] = {}
                        memory["metadata"].update(metadata)
                    
                    logger.info(f"Updated memory {memory_id} (in-memory)")
                    return {"success": True, "storage": "in-memory"}
            
            logger.warning(f"Memory {memory_id} not found for update (in-memory)")
            return {"success": False, "error": "Memory not found", "storage": "in-memory"}
        
        try:
            # MEM0 API requires a data parameter
            data = {}
            if metadata is not None:
                data["metadata"] = metadata
            
            result = self.client.update(memory_id, data)
            logger.info(f"Updated memory {memory_id} (API)")
            return {"success": True, "storage": "api", "result": result}
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            error_str = str(e)
            
            # Non-critical errors that should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            logger.error(f"Error updating memory: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                return self.update(memory_id, text, metadata)  # Try fallback
            else:
                logger.info("Non-critical error, continuing with API mode")
                return {"success": False, "error": str(e), "storage": "api"}
    
    def delete(self, memory_id: str):
        """
        Delete a specific memory
        
        Args:
            memory_id: ID of the memory to delete
            
        Returns:
            Dict with operation result
        """
        if not self.use_api or not self.api_available:
            # Try to delete from in-memory storage
            if memory_id in self._memory_ids:
                memory_info = self._memory_ids[memory_id]
                user_id = memory_info["user_id"]
                index = memory_info.get("index")
                
                if user_id in self._memory_storage and index is not None:
                    # Just set to None to maintain indices
                    self._memory_storage[user_id][index] = None
                    del self._memory_ids[memory_id]
                    logger.info(f"Deleted memory {memory_id} (in-memory)")
                    return {"success": True, "storage": "in-memory"}
            
            logger.warning(f"Memory {memory_id} not found for deletion (in-memory)")
            return {"success": False, "error": "Memory not found", "storage": "in-memory"}
        
        try:
            self.client.delete(memory_id)
            logger.info(f"Deleted memory {memory_id} (API)")
            # Also remove from our tracking if it exists
            if memory_id in self._memory_ids:
                del self._memory_ids[memory_id]
            return {"success": True, "storage": "api"}
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            error_str = str(e)
            
            # Non-critical errors that should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            logger.error(f"Error deleting memory: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                return self.delete(memory_id)  # Try fallback
            else:
                logger.info("Non-critical error, continuing with API mode")
                return {"success": False, "error": str(e), "storage": "api"}
    
    def delete_all(self, user_id: str):
        """
        Delete all memories for a user
        
        Args:
            user_id: User ID whose memories to delete
            
        Returns:
            Dict with operation result
        """
        if not self.use_api or not self.api_available:
            # Delete from in-memory storage
            if user_id in self._memory_storage:
                del self._memory_storage[user_id]
                # Remove all memory_ids for this user
                self._memory_ids = {k: v for k, v in self._memory_ids.items() 
                                   if v.get("user_id") != user_id}
                logger.info(f"Deleted all memories for user {user_id} (in-memory)")
                return {"success": True, "storage": "in-memory"}
            return {"success": False, "error": "User not found", "storage": "in-memory"}
        
        try:
            self.client.delete_all(user_id=user_id)
            logger.info(f"Deleted all memories for user {user_id} (API)")
            # Also clean up our tracking
            if user_id in self._memory_storage:
                del self._memory_storage[user_id]
            self._memory_ids = {k: v for k, v in self._memory_ids.items() 
                               if v.get("user_id") != user_id}
            return {"success": True, "storage": "api"}
        except Exception as e:
            # Determine if this is a critical error that should cause fallback
            error_str = str(e)
            
            # Non-critical errors that should not trigger fallback
            non_critical_errors = [
                "0", "index", "out of range", "list index", "empty", 
                "not found", "no results", "400", "404", "bad request",
                "invalid", "validation", "uuid", "should be a valid"
            ]
            is_critical = not any(err_text in error_str.lower() for err_text in non_critical_errors)
            
            logger.error(f"Error deleting all memories: {e}")
            
            if is_critical:
                logger.warning("Critical API error detected, falling back to in-memory storage")
                self.api_available = False
                return self.delete_all(user_id)  # Try fallback
            else:
                logger.info("Non-critical error, continuing with API mode")
                return {"success": False, "error": str(e), "storage": "api"}
    
    def delete_users(self, user_ids: List[str] = None):
        """
        Delete specified users or all users if None
        
        Args:
            user_ids: List of user IDs to delete, or None for all users
            
        Returns:
            Dict with operation result
        """
        if not self.use_api or not self.api_available:
            # Delete from in-memory storage
            if user_ids:
                for user_id in user_ids:
                    if user_id in self._memory_storage:
                        del self._memory_storage[user_id]
                # Update tracking
                self._memory_ids = {k: v for k, v in self._memory_ids.items() 
                                   if v.get("user_id") not in user_ids}
            else:
                # Delete all users
                self._memory_storage = {}
                self._memory_ids = {}
                
            logger.info(f"Deleted users: {user_ids or 'all'} (in-memory)")
            return {"success": True, "storage": "in-memory"}
        
        try:
            if user_ids:
                for user_id in user_ids:
                    self.client.delete_all(user_id=user_id)
                logger.info(f"Deleted users: {user_ids} (API)")
            else:
                self.client.delete_users()
                logger.info("Deleted all users (API)")
                
            # Clean up our tracking
            if user_ids:
                for user_id in user_ids:
                    if user_id in self._memory_storage:
                        del self._memory_storage[user_id]
                self._memory_ids = {k: v for k, v in self._memory_ids.items() 
                                   if v.get("user_id") not in user_ids}
            else:
                self._memory_storage = {}
                self._memory_ids = {}
                
            return {"success": True, "storage": "api"}
        except Exception as e:
            logger.error(f"Error deleting users: {e}")
            self.api_available = False
            return self.delete_users(user_ids)  # Try fallback
        
    def get_context(self, query: str, user_id: str = None, limit: int = 5, 
                   categories: List[str] = None, metadata: Dict = None):
        """
        Get conversation context for a query
        
        Args:
            query: Query to get context for
            user_id: User ID to get context from (optional)
            limit: Maximum number of context items
            categories: Optional categories filter
            metadata: Optional metadata filter
            
        Returns:
            Formatted context string
        """
        memories = self.search(query, user_id, limit, categories, metadata)
        
        if not memories:
            logger.info("No memories found for context generation")
            
            # If search with query returns nothing, try getting recent memories
            if user_id:
                try:
                    logger.info("Trying to get recent memories instead")
                    recent_memories = self.get_all(user_id=user_id, page=1, page_size=limit)
                    if recent_memories:
                        logger.info(f"Found {len(recent_memories)} recent memories to use as context")
                        memories = recent_memories
                    else:
                        return ""
                except Exception as e:
                    logger.error(f"Error getting recent memories: {e}")
                    return ""
            else:
                return ""
        
        context_parts = []
        
        # Process search results based on format (v1.1 format from official API or our in-memory format)
        for memory in memories:
            if "memory" in memory:  # v1.1 format from official API
                mem_data = memory.get("memory", {})
                messages = mem_data.get("messages", [])
                for message in messages:
                    role = message.get("role", "unknown")
                    content = message.get("content", "")
                    context_parts.append(f"{role}: {content}")
            elif "messages" in memory:  # Our in-memory format or older API format
                for message in memory.get("messages", []):
                    role = message.get("role", "unknown")
                    content = message.get("content", "")
                    context_parts.append(f"{role}: {content}")
        
        # Sort context parts by relevance (most relevant first)
        # In this case, we'll prioritize parts that match the query better
        if query and query.strip():
            query_terms = set(query.lower().split())
            
            def relevance_score(text):
                text_lower = text.lower()
                matched_terms = sum(1 for term in query_terms if term in text_lower)
                return matched_terms
            
            context_parts.sort(key=relevance_score, reverse=True)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_context = []
        for part in context_parts:
            part_key = part.lower()
            if part_key not in seen:
                seen.add(part_key)
                unique_context.append(part)
        
        if unique_context:
            logger.debug(f"Generated context with {len(unique_context)} unique parts")
            return "\n".join(unique_context)
        else:
            return ""
        
    def enhance_prompt(self, prompt: str, user_id: str = None, limit: int = 5,
                      categories: List[str] = None, metadata: Dict = None):
        """
        Enhance a prompt with conversation context
        
        Args:
            prompt: Original prompt
            user_id: User ID to get context from (optional)
            limit: Maximum number of context items
            categories: Optional categories filter
            metadata: Optional metadata filter
            
        Returns:
            Enhanced prompt with context
        """
        # For very short prompts, increase the limit to get more context
        if len(prompt.split()) < 10:
            limit = max(limit, 8)
        
        context = self.get_context(prompt, user_id, limit, categories, metadata)
        
        if not context:
            logger.debug(f"No context found for prompt: {prompt[:30]}...")
            return prompt
        
        context_lines = context.split('\n')
        logger.info(f"Enhanced prompt with context from {len(context_lines)} messages")
        
        # Create a more effective prompt that explicitly tells the AI about the conversation history
        enhanced_prompt = (
            f"The following is the conversation history between you and the user:\n\n"
            f"{context}\n\n"
            f"Based on this conversation history, please respond to the following:\n{prompt}"
        )
        
        # Log a sample of the enhanced prompt
        preview_length = min(200, len(enhanced_prompt))
        logger.debug(f"Enhanced prompt (first {preview_length} chars): {enhanced_prompt[:preview_length]}...")
        return enhanced_prompt

    def get_storage_status(self):
        """
        Get status information about the storage
        
        Returns:
            Dict with storage status
        """
        memory_count = 0
        user_count = len(self._memory_storage)
        
        for user_id, memories in self._memory_storage.items():
            memory_count += len(memories)
            
        return {
            "api_available": self.api_available,
            "in_memory_count": memory_count,
            "user_count": user_count,
            "storage_type": "API" if self.api_available else "in-memory"
        }

# Helper function for timestamps
def import_time():
    """Import time module and return current time"""
    try:
        from datetime import datetime
        return datetime.now
    except ImportError:
        return None

# For testing
if __name__ == "__main__":
    client = MemoryClient()
    
    # Test long-term memory with metadata
    messages = [
        {"role": "user", "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."},
        {"role": "assistant", "content": "Hello Alex! I see that you're a vegetarian with a nut allergy."}
    ]
    client.add(messages, user_id="alex", metadata={"food": "vegan"})
    
    # Test short-term memory with run_id
    messages = [
        {"role": "user", "content": "I'm planning a trip to Japan next month."},
        {"role": "assistant", "content": "That's exciting, Alex! Would you like some recommendations for vegetarian-friendly restaurants in Japan?"},
    ]
    client.add(messages, user_id="alex", run_id="trip-planning-2024")
    
    # Test search with filters
    query = "What should I cook?"
    results = client.search(query, user_id="alex", categories=["food_preferences"], metadata={"food": "vegan"})
    print(f"Found {len(results)} results for query: '{query}'")
    
    # Test get_all with filters
    short_term_memories = client.get_all(user_id="alex", run_id="trip-planning-2024", page=1, page_size=50)
    print(f"Found {len(short_term_memories)} short-term memories")
    
    # Get enhanced prompt
    enhanced = client.enhance_prompt("I'm hungry. Any ideas?", user_id="alex")
    print("\nEnhanced prompt:")
    print(enhanced)
    
    # Get status
    status = client.get_storage_status()
    print("\nStorage status:")
    print(status) 