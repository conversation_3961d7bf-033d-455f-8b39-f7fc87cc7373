"""
MEM0 Streamlit Integration
Helps integrate MEM0 memory system with Streamlit apps
"""

import streamlit as st
import uuid
import logging
import time
from typing import List, Dict, Any
from .mem0 import MemoryClient
from datetime import datetime

# Configure logging
logger = logging.getLogger("mem0_streamlit")

def get_mem0_session_id():
    """
    Get or create a MEM0 session ID for the current Streamlit session
    
    Returns:
        str: A unique session ID for the current user
    """
    # Check if session ID already exists
    if 'mem0_session_id' not in st.session_state:
        # Create a simple random session ID
        session_id = f"st-session-{uuid.uuid4().hex[:8]}"
        st.session_state.mem0_session_id = session_id
        logger.info(f"Created new MEM0 session ID: {session_id}")
        
    return st.session_state.mem0_session_id

def get_mem0_client(api_key=None):
    """
    Get or create a MEM0 client for the current session
    
    Args:
        api_key: Optional API key (will use environment variable if not provided)
        
    Returns:
        MemoryClient: Initialized MEM0 client
    """
    # Initialize client if not already done
    if 'mem0_client' not in st.session_state:
        st.session_state.mem0_client = MemoryClient(api_key=api_key)
        logger.info(f"Created new MEM0 client, API available: {st.session_state.mem0_client.api_available}")
        
    return st.session_state.mem0_client

def store_conversation(user_message: str, assistant_message: str):
    """
    Store a conversation exchange in MEM0
    
    Args:
        user_message: The user's message
        assistant_message: The assistant's response
        
    Returns:
        bool: True if successful, False otherwise
    """
    client = get_mem0_client()
    session_id = get_mem0_session_id()
    
    # Format as messages for MEM0
    messages = [
        {"role": "user", "content": user_message},
        {"role": "assistant", "content": assistant_message}
    ]
    
    # Create basic metadata
    metadata = {
        "source": "a2a_system",
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id,
        "input_length": len(user_message),
        "response_length": len(assistant_message),
        "memory_enhanced": st.session_state.get('use_memory', True)
    }
    
    # Store in MEM0 with metadata and categories
    logging.info(f"Storing conversation with {len(messages)} messages for user {session_id}")
    result = client.add(
        messages, 
        user_id=session_id,
        metadata=metadata,
        categories=["conversation", "a2a_system"]
    )
    
    # Invalidate cache to force refresh
    cache_key = f"mem0_count_{session_id}"
    if cache_key in st.session_state:
        del st.session_state[cache_key]
    
    return result.get("success", False)

def enhance_user_query(query: str):
    """
    Enhance a user query with relevant context from past conversations
    
    Args:
        query: The user's query
        
    Returns:
        str: Enhanced query with relevant context
    """
    client = get_mem0_client()
    session_id = get_mem0_session_id()
    
    logger.info(f"Enhancing query: {query[:30]}... for user {session_id}")
    
    # Determine query complexity for better context retrieval
    query_words = len(query.split())
    if query_words > 50:
        complexity = "complex"
        limit = 8
    elif query_words > 20:
        complexity = "medium"
        limit = 6
    else:
        complexity = "simple"
        limit = 4
    
    # Try multiple approaches to find relevant context
    enhanced_prompt = query
    
    # Approach 1: Try with metadata filter
    try:
        metadata_filter = {
            "source": "a2a_system",
            "complexity": complexity
        }
        
        enhanced_prompt = client.enhance_prompt(
            query, 
            user_id=session_id, 
            limit=limit,
            categories=["conversation", "a2a_system"],
            metadata=metadata_filter
        )
        
        if enhanced_prompt != query:
            logger.info(f"Found relevant context with metadata filter")
            return enhanced_prompt
            
    except Exception as e:
        logger.warning(f"Error with metadata filter: {e}")
    
    # Approach 2: Try without metadata filter (search by content)
    try:
        enhanced_prompt = client.enhance_prompt(
            query, 
            user_id=session_id, 
            limit=limit,
            categories=["conversation", "a2a_system"]
        )
        
        if enhanced_prompt != query:
            logger.info(f"Found relevant context without metadata filter")
            return enhanced_prompt
            
    except Exception as e:
        logger.warning(f"Error without metadata filter: {e}")
    
    # Approach 3: Try with higher limit to get more context
    try:
        enhanced_prompt = client.enhance_prompt(
            query, 
            user_id=session_id, 
            limit=limit + 2,  # Increase limit
            categories=["conversation", "a2a_system"]
        )
        
        if enhanced_prompt != query:
            logger.info(f"Found relevant context with higher limit")
            return enhanced_prompt
            
    except Exception as e:
        logger.warning(f"Error with higher limit: {e}")
    
    # Approach 4: Try getting recent memories directly
    try:
        memories_response = client.get_all(
            user_id=session_id,
            page=1,
            page_size=5  # Get recent 5 memories
        )
        
        # Handle pagination structure
        memories = []
        if memories_response and isinstance(memories_response, dict):
            if 'results' in memories_response:
                memories = memories_response['results']
            elif 'data' in memories_response:
                memories = memories_response['data']
            else:
                # If it's not pagination structure, treat as direct list
                memories = memories_response if isinstance(memories_response, list) else []
        elif isinstance(memories_response, list):
            memories = memories_response
        
        if memories and len(memories) > 0:
            # Ensure memories is a list
            if not isinstance(memories, list):
                memories = list(memories) if hasattr(memories, '__iter__') else []
            
            if len(memories) > 0:
                # Build context from recent memories
                context_parts = []
                # Use last 3 memories (or all if less than 3)
                memories_to_use = memories[-3:] if len(memories) >= 3 else memories
                
                for memory in memories_to_use:
                    if isinstance(memory, dict) and 'messages' in memory:
                        for msg in memory['messages']:
                            if isinstance(msg, dict) and msg.get('role') in ['user', 'assistant']:
                                content = msg.get('content', '')
                                if content and len(content) > 10:  # Only add meaningful content
                                    context_parts.append(f"{msg['role']}: {content}")
                
                if context_parts:
                    # Use last 6 messages (or all if less than 6)
                    last_messages = context_parts[-6:] if len(context_parts) >= 6 else context_parts
                    context = "\n".join(last_messages)
                    enhanced_prompt = f"The following is the conversation history between you and the user:\n\n{context}\n\nCurrent user query: {query}"
                    logger.info(f"Built context from recent memories")
                    return enhanced_prompt
                
    except Exception as e:
        logger.warning(f"Error getting recent memories: {e}")
    
    # If no context found, return original query
    logger.info(f"No relevant context found, using original query")
    return query

def count_user_memories(session_id: str = None) -> int:
    """Count memories for a specific user session"""
    if session_id is None:
        session_id = get_mem0_session_id()
    
    # Check cache first
    cache_key = f"memory_count_{session_id}"
    current_time = time.time()
    
    if cache_key in st.session_state:
        cached_data = st.session_state[cache_key]
        # Cache for 30 seconds
        if current_time - cached_data["timestamp"] < 30:
            return cached_data["count"]
    
    try:
        client = get_mem0_client()
        
        # Get memories without categories filter
        memories_response = client.get_all(
            user_id=session_id,
            page=1,
            page_size=1000  # Get all memories for this user
        )
        
        # Handle pagination structure
        memories = []
        if memories_response and isinstance(memories_response, dict):
            if 'results' in memories_response:
                memories = memories_response['results']
            elif 'data' in memories_response:
                memories = memories_response['data']
            else:
                # If it's not pagination structure, treat as direct list
                memories = memories_response if isinstance(memories_response, list) else []
        elif isinstance(memories_response, list):
            memories = memories_response
        
        count = len(memories) if memories else 0
        
        # Cache the result
        st.session_state[cache_key] = {
            "count": count,
            "timestamp": current_time
        }
        
        logger.info(f"Counted {count} memories for user {session_id}")
        return count
        
    except Exception as e:
        logger.error(f"Error counting memories for user {session_id}: {e}")
        return -1

def show_mem0_status():
    """Display MEM0 status in the sidebar"""
    client = get_mem0_client()
    session_id = get_mem0_session_id()
    
    # Get storage status
    storage_status = client.get_storage_status()
    
    # Count memories for this user
    memory_count = count_user_memories(session_id)
    
    # Get some sample memories to show metadata info
    memories_response = client.get_all(
        user_id=session_id,
        page=1,
        page_size=3
    )
    
    # Handle pagination structure
    sample_memories = []
    if memories_response and isinstance(memories_response, dict):
        if 'results' in memories_response:
            sample_memories = memories_response['results']
        elif 'data' in memories_response:
            sample_memories = memories_response['data']
        else:
            # If it's not pagination structure, treat as direct list
            sample_memories = memories_response if isinstance(memories_response, list) else []
    elif isinstance(memories_response, list):
        sample_memories = memories_response
    
    # Display status
    if storage_status["api_available"]:
        st.success("✅ MEM0 API Connected")
        
        # Show memory count
        st.metric("Memories", memory_count)
        
        # Show metadata statistics if we have memories
        if sample_memories and len(sample_memories) > 0:
            st.subheader("📊 Memory Analytics")
            
            # Count by complexity
            complexity_counts = {}
            agent_counts = {}
            
            for memory in sample_memories:
                # Ensure memory is a dictionary
                if isinstance(memory, dict):
                    metadata = memory.get("metadata", {})
                    
                    # Count by complexity
                    complexity = metadata.get("complexity", "unknown")
                    complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1
                    
                    # Count by agent
                    agent = metadata.get("agent", "unknown")
                    agent_counts[agent] = agent_counts.get(agent, 0) + 1
            
            # Display complexity distribution
            if complexity_counts:
                st.write("**Complexity Distribution:**")
                for complexity, count in complexity_counts.items():
                    st.write(f"  • {complexity}: {count}")
            
            # Display agent distribution
            if agent_counts:
                st.write("**Agent Distribution:**")
                for agent, count in agent_counts.items():
                    st.write(f"  • {agent}: {count}")
            
            # Show latest memory metadata
            if sample_memories:
                latest = sample_memories[0]
                if isinstance(latest, dict):
                    metadata = latest.get("metadata", {})
                    
                    st.write("**Latest Memory:**")
                    if "timestamp" in metadata:
                        st.write(f"  • Time: {metadata['timestamp'][:19]}")
                    if "processing_time" in metadata:
                        st.write(f"  • Processing: {metadata['processing_time']:.2f}s")
                    if "agent" in metadata:
                        st.write(f"  • Agent: {metadata['agent']}")
        
    else:
        st.warning("⚠️ MEM0 Using In-Memory Storage")
        st.info(f"Memories: {memory_count}")
    
    # Add refresh button
    if st.button("🔄 Refresh Status"):
        st.session_state.refresh_mem0_status = True
        st.rerun() 