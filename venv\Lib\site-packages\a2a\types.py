# generated by datamodel-codegen:
#   filename:  https://raw.githubusercontent.com/a2aproject/A2A/refs/heads/main/specification/json/a2a.json

from __future__ import annotations

from enum import Enum
from typing import Any, Literal

from pydantic import BaseModel, Field, RootModel


class A2A(RootModel[Any]):
    root: Any


class In(str, Enum):
    """
    The location of the API key. Valid values are "query", "header", or "cookie".
    """

    cookie = 'cookie'
    header = 'header'
    query = 'query'


class APIKeySecurityScheme(BaseModel):
    """
    API Key security scheme.
    """

    description: str | None = None
    """
    Description of this security scheme.
    """
    in_: In = Field(..., alias='in')
    """
    The location of the API key. Valid values are "query", "header", or "cookie".
    """
    name: str
    """
    The name of the header, query or cookie parameter to be used.
    """
    type: Literal['apiKey'] = 'apiKey'


class AgentExtension(BaseModel):
    """
    A declaration of an extension supported by an Agent.
    """

    description: str | None = None
    """
    A description of how this agent uses this extension.
    """
    params: dict[str, Any] | None = None
    """
    Optional configuration for the extension.
    """
    required: bool | None = None
    """
    Whether the client must follow specific requirements of the extension.
    """
    uri: str
    """
    The URI of the extension.
    """


class AgentInterface(BaseModel):
    """
    AgentInterface provides a declaration of a combination of the
    target url and the supported transport to interact with the agent.
    """

    transport: str
    """
    The transport supported this url. This is an open form string, to be
    easily extended for many transport protocols. The core ones officially
    supported are JSONRPC, GRPC and HTTP+JSON.
    """
    url: str


class AgentProvider(BaseModel):
    """
    Represents the service provider of an agent.
    """

    organization: str
    """
    Agent provider's organization name.
    """
    url: str
    """
    Agent provider's URL.
    """


class AgentSkill(BaseModel):
    """
    Represents a unit of capability that an agent can perform.
    """

    description: str
    """
    Description of the skill - will be used by the client or a human
    as a hint to understand what the skill does.
    """
    examples: list[str] | None = Field(
        default=None, examples=[['I need a recipe for bread']]
    )
    """
    The set of example scenarios that the skill can perform.
    Will be used by the client as a hint to understand how the skill can be used.
    """
    id: str
    """
    Unique identifier for the agent's skill.
    """
    inputModes: list[str] | None = None
    """
    The set of interaction modes that the skill supports
    (if different than the default).
    Supported media types for input.
    """
    name: str
    """
    Human readable name of the skill.
    """
    outputModes: list[str] | None = None
    """
    Supported media types for output.
    """
    tags: list[str] = Field(..., examples=[['cooking', 'customer support', 'billing']])
    """
    Set of tagwords describing classes of capabilities for this specific skill.
    """


class AuthorizationCodeOAuthFlow(BaseModel):
    """
    Configuration details for a supported OAuth Flow
    """

    authorizationUrl: str
    """
    The authorization URL to be used for this flow. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS
    """
    refreshUrl: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope name and a short
    description for it. The map MAY be empty.
    """
    tokenUrl: str
    """
    The token URL to be used for this flow. This MUST be in the form of a URL. The OAuth2 standard
    requires the use of TLS.
    """


class ClientCredentialsOAuthFlow(BaseModel):
    """
    Configuration details for a supported OAuth Flow
    """

    refreshUrl: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope name and a short
    description for it. The map MAY be empty.
    """
    tokenUrl: str
    """
    The token URL to be used for this flow. This MUST be in the form of a URL. The OAuth2 standard
    requires the use of TLS.
    """


class ContentTypeNotSupportedError(BaseModel):
    """
    A2A specific error indicating incompatible content types between request and agent capabilities.
    """

    code: Literal[-32005] = -32005
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Incompatible content types'
    """
    A String providing a short description of the error.
    """


class DataPart(BaseModel):
    """
    Represents a structured data segment within a message part.
    """

    data: dict[str, Any]
    """
    Structured data content
    """
    kind: Literal['data'] = 'data'
    """
    Part type - data for DataParts
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the part.
    """


class DeleteTaskPushNotificationConfigParams(BaseModel):
    """
    Parameters for removing pushNotificationConfiguration associated with a Task
    """

    id: str
    """
    Task id.
    """
    metadata: dict[str, Any] | None = None
    pushNotificationConfigId: str


class DeleteTaskPushNotificationConfigRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/pushNotificationConfig/delete' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/delete'] = (
        'tasks/pushNotificationConfig/delete'
    )
    """
    A String containing the name of the method to be invoked.
    """
    params: DeleteTaskPushNotificationConfigParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class DeleteTaskPushNotificationConfigSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'tasks/pushNotificationConfig/delete' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: None
    """
    The result object on success.
    """


class FileBase(BaseModel):
    """
    Represents the base entity for FileParts
    """

    mimeType: str | None = None
    """
    Optional mimeType for the file
    """
    name: str | None = None
    """
    Optional name for the file
    """


class FileWithBytes(BaseModel):
    """
    Define the variant where 'bytes' is present and 'uri' is absent
    """

    bytes: str
    """
    base64 encoded content of the file
    """
    mimeType: str | None = None
    """
    Optional mimeType for the file
    """
    name: str | None = None
    """
    Optional name for the file
    """


class FileWithUri(BaseModel):
    """
    Define the variant where 'uri' is present and 'bytes' is absent
    """

    mimeType: str | None = None
    """
    Optional mimeType for the file
    """
    name: str | None = None
    """
    Optional name for the file
    """
    uri: str
    """
    URL for the File content
    """


class GetTaskPushNotificationConfigParams(BaseModel):
    """
    Parameters for fetching a pushNotificationConfiguration associated with a Task
    """

    id: str
    """
    Task id.
    """
    metadata: dict[str, Any] | None = None
    pushNotificationConfigId: str | None = None


class HTTPAuthSecurityScheme(BaseModel):
    """
    HTTP Authentication security scheme.
    """

    bearerFormat: str | None = None
    """
    A hint to the client to identify how the bearer token is formatted. Bearer tokens are usually
    generated by an authorization server, so this information is primarily for documentation
    purposes.
    """
    description: str | None = None
    """
    Description of this security scheme.
    """
    scheme: str
    """
    The name of the HTTP Authentication scheme to be used in the Authorization header as defined
    in RFC7235. The values used SHOULD be registered in the IANA Authentication Scheme registry.
    The value is case-insensitive, as defined in RFC7235.
    """
    type: Literal['http'] = 'http'


class ImplicitOAuthFlow(BaseModel):
    """
    Configuration details for a supported OAuth Flow
    """

    authorizationUrl: str
    """
    The authorization URL to be used for this flow. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS
    """
    refreshUrl: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope name and a short
    description for it. The map MAY be empty.
    """


class InternalError(BaseModel):
    """
    JSON-RPC error indicating an internal JSON-RPC error on the server.
    """

    code: Literal[-32603] = -32603
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Internal error'
    """
    A String providing a short description of the error.
    """


class InvalidAgentResponseError(BaseModel):
    """
    A2A specific error indicating agent returned invalid response for the current method
    """

    code: Literal[-32006] = -32006
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid agent response'
    """
    A String providing a short description of the error.
    """


class InvalidParamsError(BaseModel):
    """
    JSON-RPC error indicating invalid method parameter(s).
    """

    code: Literal[-32602] = -32602
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid parameters'
    """
    A String providing a short description of the error.
    """


class InvalidRequestError(BaseModel):
    """
    JSON-RPC error indicating the JSON sent is not a valid Request object.
    """

    code: Literal[-32600] = -32600
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Request payload validation error'
    """
    A String providing a short description of the error.
    """


class JSONParseError(BaseModel):
    """
    JSON-RPC error indicating invalid JSON was received by the server.
    """

    code: Literal[-32700] = -32700
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid JSON payload'
    """
    A String providing a short description of the error.
    """


class JSONRPCError(BaseModel):
    """
    Represents a JSON-RPC 2.0 Error object.
    This is typically included in a JSONRPCErrorResponse when an error occurs.
    """

    code: int
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str
    """
    A String providing a short description of the error.
    """


class JSONRPCMessage(BaseModel):
    """
    Base interface for any JSON-RPC 2.0 request or response.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """


class JSONRPCRequest(BaseModel):
    """
    Represents a JSON-RPC 2.0 Request object.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: str
    """
    A String containing the name of the method to be invoked.
    """
    params: dict[str, Any] | None = None
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class JSONRPCSuccessResponse(BaseModel):
    """
    Represents a JSON-RPC 2.0 Success Response object.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Any
    """
    The result object on success
    """


class ListTaskPushNotificationConfigParams(BaseModel):
    """
    Parameters for getting list of pushNotificationConfigurations associated with a Task
    """

    id: str
    """
    Task id.
    """
    metadata: dict[str, Any] | None = None


class ListTaskPushNotificationConfigRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/pushNotificationConfig/list' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/list'] = (
        'tasks/pushNotificationConfig/list'
    )
    """
    A String containing the name of the method to be invoked.
    """
    params: ListTaskPushNotificationConfigParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class Role(str, Enum):
    """
    Message sender's role
    """

    agent = 'agent'
    user = 'user'


class MethodNotFoundError(BaseModel):
    """
    JSON-RPC error indicating the method does not exist or is not available.
    """

    code: Literal[-32601] = -32601
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Method not found'
    """
    A String providing a short description of the error.
    """


class OpenIdConnectSecurityScheme(BaseModel):
    """
    OpenID Connect security scheme configuration.
    """

    description: str | None = None
    """
    Description of this security scheme.
    """
    openIdConnectUrl: str
    """
    Well-known URL to discover the [[OpenID-Connect-Discovery]] provider metadata.
    """
    type: Literal['openIdConnect'] = 'openIdConnect'


class PartBase(BaseModel):
    """
    Base properties common to all message parts.
    """

    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the part.
    """


class PasswordOAuthFlow(BaseModel):
    """
    Configuration details for a supported OAuth Flow
    """

    refreshUrl: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be in the form of a URL. The OAuth2
    standard requires the use of TLS.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope name and a short
    description for it. The map MAY be empty.
    """
    tokenUrl: str
    """
    The token URL to be used for this flow. This MUST be in the form of a URL. The OAuth2 standard
    requires the use of TLS.
    """


class PushNotificationAuthenticationInfo(BaseModel):
    """
    Defines authentication details for push notifications.
    """

    credentials: str | None = None
    """
    Optional credentials
    """
    schemes: list[str]
    """
    Supported authentication schemes - e.g. Basic, Bearer
    """


class PushNotificationConfig(BaseModel):
    """
    Configuration for setting up push notifications for task updates.
    """

    authentication: PushNotificationAuthenticationInfo | None = None
    id: str | None = None
    """
    Push Notification ID - created by server to support multiple callbacks
    """
    token: str | None = None
    """
    Token unique to this task/session.
    """
    url: str
    """
    URL for sending the push notifications.
    """


class PushNotificationNotSupportedError(BaseModel):
    """
    A2A specific error indicating the agent does not support push notifications.
    """

    code: Literal[-32003] = -32003
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Push Notification is not supported'
    """
    A String providing a short description of the error.
    """


class SecuritySchemeBase(BaseModel):
    """
    Base properties shared by all security schemes.
    """

    description: str | None = None
    """
    Description of this security scheme.
    """


class TaskIdParams(BaseModel):
    """
    Parameters containing only a task ID, used for simple task operations.
    """

    id: str
    """
    Task id.
    """
    metadata: dict[str, Any] | None = None


class TaskNotCancelableError(BaseModel):
    """
    A2A specific error indicating the task is in a state where it cannot be canceled.
    """

    code: Literal[-32002] = -32002
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Task cannot be canceled'
    """
    A String providing a short description of the error.
    """


class TaskNotFoundError(BaseModel):
    """
    A2A specific error indicating the requested task ID was not found.
    """

    code: Literal[-32001] = -32001
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Task not found'
    """
    A String providing a short description of the error.
    """


class TaskPushNotificationConfig(BaseModel):
    """
    Parameters for setting or getting push notification configuration for a task
    """

    pushNotificationConfig: PushNotificationConfig
    """
    Push notification configuration.
    """
    taskId: str
    """
    Task id.
    """


class TaskQueryParams(BaseModel):
    """
    Parameters for querying a task, including optional history length.
    """

    historyLength: int | None = None
    """
    Number of recent messages to be retrieved.
    """
    id: str
    """
    Task id.
    """
    metadata: dict[str, Any] | None = None


class TaskResubscriptionRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/resubscribe' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/resubscribe'] = 'tasks/resubscribe'
    """
    A String containing the name of the method to be invoked.
    """
    params: TaskIdParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class TaskState(str, Enum):
    """
    Represents the possible states of a Task.
    """

    submitted = 'submitted'
    working = 'working'
    input_required = 'input-required'
    completed = 'completed'
    canceled = 'canceled'
    failed = 'failed'
    rejected = 'rejected'
    auth_required = 'auth-required'
    unknown = 'unknown'


class TextPart(BaseModel):
    """
    Represents a text segment within parts.
    """

    kind: Literal['text'] = 'text'
    """
    Part type - text for TextParts
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the part.
    """
    text: str
    """
    Text content
    """


class UnsupportedOperationError(BaseModel):
    """
    A2A specific error indicating the requested operation is not supported by the agent.
    """

    code: Literal[-32004] = -32004
    """
    A Number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A Primitive or Structured value that contains additional information about the error.
    This may be omitted.
    """
    message: str | None = 'This operation is not supported'
    """
    A String providing a short description of the error.
    """


class A2AError(
    RootModel[
        JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    ]
):
    root: (
        JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    )


class AgentCapabilities(BaseModel):
    """
    Defines optional capabilities supported by an agent.
    """

    extensions: list[AgentExtension] | None = None
    """
    extensions supported by this agent.
    """
    pushNotifications: bool | None = None
    """
    true if the agent can notify updates to client.
    """
    stateTransitionHistory: bool | None = None
    """
    true if the agent exposes status change history for tasks.
    """
    streaming: bool | None = None
    """
    true if the agent supports SSE.
    """


class CancelTaskRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/cancel' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/cancel'] = 'tasks/cancel'
    """
    A String containing the name of the method to be invoked.
    """
    params: TaskIdParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class FilePart(BaseModel):
    """
    Represents a File segment within parts.
    """

    file: FileWithBytes | FileWithUri
    """
    File content either as url or bytes
    """
    kind: Literal['file'] = 'file'
    """
    Part type - file for FileParts
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the part.
    """


class GetTaskPushNotificationConfigRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/pushNotificationConfig/get' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/get'] = (
        'tasks/pushNotificationConfig/get'
    )
    """
    A String containing the name of the method to be invoked.
    """
    params: TaskIdParams | GetTaskPushNotificationConfigParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    TaskIdParams type is deprecated for this method use `GetTaskPushNotificationConfigParams` instead.
    """


class GetTaskPushNotificationConfigSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'tasks/pushNotificationConfig/get' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: TaskPushNotificationConfig
    """
    The result object on success.
    """


class GetTaskRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/get' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/get'] = 'tasks/get'
    """
    A String containing the name of the method to be invoked.
    """
    params: TaskQueryParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class JSONRPCErrorResponse(BaseModel):
    """
    Represents a JSON-RPC 2.0 Error Response object.
    """

    error: (
        JSONRPCError
        | JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    )
    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """


class ListTaskPushNotificationConfigSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'tasks/pushNotificationConfig/list' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: list[TaskPushNotificationConfig]
    """
    The result object on success.
    """


class MessageSendConfiguration(BaseModel):
    """
    Configuration for the send message request.
    """

    acceptedOutputModes: list[str]
    """
    Accepted output modalities by the client.
    """
    blocking: bool | None = None
    """
    If the server should treat the client as a blocking request.
    """
    historyLength: int | None = None
    """
    Number of recent messages to be retrieved.
    """
    pushNotificationConfig: PushNotificationConfig | None = None
    """
    Where the server should send notifications when disconnected.
    """


class OAuthFlows(BaseModel):
    """
    Allows configuration of the supported OAuth Flows
    """

    authorizationCode: AuthorizationCodeOAuthFlow | None = None
    """
    Configuration for the OAuth Authorization Code flow. Previously called accessCode in OpenAPI 2.0.
    """
    clientCredentials: ClientCredentialsOAuthFlow | None = None
    """
    Configuration for the OAuth Client Credentials flow. Previously called application in OpenAPI 2.0
    """
    implicit: ImplicitOAuthFlow | None = None
    """
    Configuration for the OAuth Implicit flow
    """
    password: PasswordOAuthFlow | None = None
    """
    Configuration for the OAuth Resource Owner Password flow
    """


class Part(RootModel[TextPart | FilePart | DataPart]):
    root: TextPart | FilePart | DataPart
    """
    Represents a part of a message, which can be text, a file, or structured data.
    """


class SetTaskPushNotificationConfigRequest(BaseModel):
    """
    JSON-RPC request model for the 'tasks/pushNotificationConfig/set' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/set'] = (
        'tasks/pushNotificationConfig/set'
    )
    """
    A String containing the name of the method to be invoked.
    """
    params: TaskPushNotificationConfig
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class SetTaskPushNotificationConfigSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'tasks/pushNotificationConfig/set' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: TaskPushNotificationConfig
    """
    The result object on success.
    """


class Artifact(BaseModel):
    """
    Represents an artifact generated for a task.
    """

    artifactId: str
    """
    Unique identifier for the artifact.
    """
    description: str | None = None
    """
    Optional description for the artifact.
    """
    extensions: list[str] | None = None
    """
    The URIs of extensions that are present or contributed to this Artifact.
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """
    name: str | None = None
    """
    Optional name for the artifact.
    """
    parts: list[Part]
    """
    Artifact parts.
    """


class DeleteTaskPushNotificationConfigResponse(
    RootModel[JSONRPCErrorResponse | DeleteTaskPushNotificationConfigSuccessResponse]
):
    root: JSONRPCErrorResponse | DeleteTaskPushNotificationConfigSuccessResponse
    """
    JSON-RPC response for the 'tasks/pushNotificationConfig/delete' method.
    """


class GetTaskPushNotificationConfigResponse(
    RootModel[JSONRPCErrorResponse | GetTaskPushNotificationConfigSuccessResponse]
):
    root: JSONRPCErrorResponse | GetTaskPushNotificationConfigSuccessResponse
    """
    JSON-RPC response for the 'tasks/pushNotificationConfig/set' method.
    """


class ListTaskPushNotificationConfigResponse(
    RootModel[JSONRPCErrorResponse | ListTaskPushNotificationConfigSuccessResponse]
):
    root: JSONRPCErrorResponse | ListTaskPushNotificationConfigSuccessResponse
    """
    JSON-RPC response for the 'tasks/pushNotificationConfig/list' method.
    """


class Message(BaseModel):
    """
    Represents a single message exchanged between user and agent.
    """

    contextId: str | None = None
    """
    The context the message is associated with
    """
    extensions: list[str] | None = None
    """
    The URIs of extensions that are present or contributed to this Message.
    """
    kind: Literal['message'] = 'message'
    """
    Event type
    """
    messageId: str
    """
    Identifier created by the message creator
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """
    parts: list[Part]
    """
    Message content
    """
    referenceTaskIds: list[str] | None = None
    """
    List of tasks referenced as context by this message.
    """
    role: Role
    """
    Message sender's role
    """
    taskId: str | None = None
    """
    Identifier of task the message is related to
    """


class MessageSendParams(BaseModel):
    """
    Sent by the client to the agent as a request. May create, continue or restart a task.
    """

    configuration: MessageSendConfiguration | None = None
    """
    Send message configuration.
    """
    message: Message
    """
    The message being sent to the server.
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """


class OAuth2SecurityScheme(BaseModel):
    """
    OAuth2.0 security scheme configuration.
    """

    description: str | None = None
    """
    Description of this security scheme.
    """
    flows: OAuthFlows
    """
    An object containing configuration information for the flow types supported.
    """
    type: Literal['oauth2'] = 'oauth2'


class SecurityScheme(
    RootModel[
        APIKeySecurityScheme
        | HTTPAuthSecurityScheme
        | OAuth2SecurityScheme
        | OpenIdConnectSecurityScheme
    ]
):
    root: (
        APIKeySecurityScheme
        | HTTPAuthSecurityScheme
        | OAuth2SecurityScheme
        | OpenIdConnectSecurityScheme
    )
    """
    Mirrors the OpenAPI Security Scheme Object
    (https://swagger.io/specification/#security-scheme-object)
    """


class SendMessageRequest(BaseModel):
    """
    JSON-RPC request model for the 'message/send' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['message/send'] = 'message/send'
    """
    A String containing the name of the method to be invoked.
    """
    params: MessageSendParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class SendStreamingMessageRequest(BaseModel):
    """
    JSON-RPC request model for the 'message/stream' method.
    """

    id: str | int
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['message/stream'] = 'message/stream'
    """
    A String containing the name of the method to be invoked.
    """
    params: MessageSendParams
    """
    A Structured value that holds the parameter values to be used during the invocation of the method.
    """


class SetTaskPushNotificationConfigResponse(
    RootModel[JSONRPCErrorResponse | SetTaskPushNotificationConfigSuccessResponse]
):
    root: JSONRPCErrorResponse | SetTaskPushNotificationConfigSuccessResponse
    """
    JSON-RPC response for the 'tasks/pushNotificationConfig/set' method.
    """


class TaskArtifactUpdateEvent(BaseModel):
    """
    Sent by server during sendStream or subscribe requests
    """

    append: bool | None = None
    """
    Indicates if this artifact appends to a previous one
    """
    artifact: Artifact
    """
    Generated artifact
    """
    contextId: str
    """
    The context the task is associated with
    """
    kind: Literal['artifact-update'] = 'artifact-update'
    """
    Event type
    """
    lastChunk: bool | None = None
    """
    Indicates if this is the last chunk of the artifact
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """
    taskId: str
    """
    Task id
    """


class TaskStatus(BaseModel):
    """
    TaskState and accompanying message.
    """

    message: Message | None = None
    """
    Additional status updates for client
    """
    state: TaskState
    timestamp: str | None = Field(default=None, examples=['2023-10-27T10:00:00Z'])
    """
    ISO 8601 datetime string when the status was recorded.
    """


class TaskStatusUpdateEvent(BaseModel):
    """
    Sent by server during sendStream or subscribe requests
    """

    contextId: str
    """
    The context the task is associated with
    """
    final: bool
    """
    Indicates the end of the event stream
    """
    kind: Literal['status-update'] = 'status-update'
    """
    Event type
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """
    status: TaskStatus
    """
    Current status of the task
    """
    taskId: str
    """
    Task id
    """


class A2ARequest(
    RootModel[
        SendMessageRequest
        | SendStreamingMessageRequest
        | GetTaskRequest
        | CancelTaskRequest
        | SetTaskPushNotificationConfigRequest
        | GetTaskPushNotificationConfigRequest
        | TaskResubscriptionRequest
        | ListTaskPushNotificationConfigRequest
        | DeleteTaskPushNotificationConfigRequest
    ]
):
    root: (
        SendMessageRequest
        | SendStreamingMessageRequest
        | GetTaskRequest
        | CancelTaskRequest
        | SetTaskPushNotificationConfigRequest
        | GetTaskPushNotificationConfigRequest
        | TaskResubscriptionRequest
        | ListTaskPushNotificationConfigRequest
        | DeleteTaskPushNotificationConfigRequest
    )
    """
    A2A supported request types
    """


class AgentCard(BaseModel):
    """
    An AgentCard conveys key information:
    - Overall details (version, name, description, uses)
    - Skills: A set of capabilities the agent can perform
    - Default modalities/content types supported by the agent.
    - Authentication requirements
    """

    additionalInterfaces: list[AgentInterface] | None = None
    """
    Announcement of additional supported transports. Client can use any of
    the supported transports.
    """
    capabilities: AgentCapabilities
    """
    Optional capabilities supported by the agent.
    """
    defaultInputModes: list[str]
    """
    The set of interaction modes that the agent supports across all skills. This can be overridden per-skill.
    Supported media types for input.
    """
    defaultOutputModes: list[str]
    """
    Supported media types for output.
    """
    description: str = Field(
        ..., examples=['Agent that helps users with recipes and cooking.']
    )
    """
    A human-readable description of the agent. Used to assist users and
    other agents in understanding what the agent can do.
    """
    documentationUrl: str | None = None
    """
    A URL to documentation for the agent.
    """
    iconUrl: str | None = None
    """
    A URL to an icon for the agent.
    """
    name: str = Field(..., examples=['Recipe Agent'])
    """
    Human readable name of the agent.
    """
    preferredTransport: str | None = None
    """
    The transport of the preferred endpoint. If empty, defaults to JSONRPC.
    """
    protocolVersion: str | None = '0.2.5'
    """
    The version of the A2A protocol this agent supports.
    """
    provider: AgentProvider | None = None
    """
    The service provider of the agent
    """
    security: list[dict[str, list[str]]] | None = None
    """
    Security requirements for contacting the agent.
    """
    securitySchemes: dict[str, SecurityScheme] | None = None
    """
    Security scheme details used for authenticating with this agent.
    """
    skills: list[AgentSkill]
    """
    Skills are a unit of capability that an agent can perform.
    """
    supportsAuthenticatedExtendedCard: bool | None = None
    """
    true if the agent supports providing an extended agent card when the user is authenticated.
    Defaults to false if not specified.
    """
    url: str
    """
    A URL to the address the agent is hosted at. This represents the
    preferred endpoint as declared by the agent.
    """
    version: str = Field(..., examples=['1.0.0'])
    """
    The version of the agent - format is up to the provider.
    """


class Task(BaseModel):
    artifacts: list[Artifact] | None = None
    """
    Collection of artifacts created by the agent.
    """
    contextId: str
    """
    Server-generated id for contextual alignment across interactions
    """
    history: list[Message] | None = None
    id: str
    """
    Unique identifier for the task
    """
    kind: Literal['task'] = 'task'
    """
    Event type
    """
    metadata: dict[str, Any] | None = None
    """
    Extension metadata.
    """
    status: TaskStatus
    """
    Current status of the task
    """


class CancelTaskSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'tasks/cancel' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task
    """
    The result object on success.
    """


class GetTaskSuccessResponse(BaseModel):
    """
    JSON-RPC success response for the 'tasks/get' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task
    """
    The result object on success.
    """


class SendMessageSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'message/send' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task | Message
    """
    The result object on success
    """


class SendStreamingMessageSuccessResponse(BaseModel):
    """
    JSON-RPC success response model for the 'message/stream' method.
    """

    id: str | int | None = None
    """
    An identifier established by the Client that MUST contain a String, Number.
    Numbers SHOULD NOT contain fractional parts.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    Specifies the version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task | Message | TaskStatusUpdateEvent | TaskArtifactUpdateEvent
    """
    The result object on success
    """


class CancelTaskResponse(RootModel[JSONRPCErrorResponse | CancelTaskSuccessResponse]):
    root: JSONRPCErrorResponse | CancelTaskSuccessResponse
    """
    JSON-RPC response for the 'tasks/cancel' method.
    """


class GetTaskResponse(RootModel[JSONRPCErrorResponse | GetTaskSuccessResponse]):
    root: JSONRPCErrorResponse | GetTaskSuccessResponse
    """
    JSON-RPC response for the 'tasks/get' method.
    """


class JSONRPCResponse(
    RootModel[
        JSONRPCErrorResponse
        | SendMessageSuccessResponse
        | SendStreamingMessageSuccessResponse
        | GetTaskSuccessResponse
        | CancelTaskSuccessResponse
        | SetTaskPushNotificationConfigSuccessResponse
        | GetTaskPushNotificationConfigSuccessResponse
        | ListTaskPushNotificationConfigSuccessResponse
        | DeleteTaskPushNotificationConfigSuccessResponse
    ]
):
    root: (
        JSONRPCErrorResponse
        | SendMessageSuccessResponse
        | SendStreamingMessageSuccessResponse
        | GetTaskSuccessResponse
        | CancelTaskSuccessResponse
        | SetTaskPushNotificationConfigSuccessResponse
        | GetTaskPushNotificationConfigSuccessResponse
        | ListTaskPushNotificationConfigSuccessResponse
        | DeleteTaskPushNotificationConfigSuccessResponse
    )
    """
    Represents a JSON-RPC 2.0 Response object.
    """


class SendMessageResponse(RootModel[JSONRPCErrorResponse | SendMessageSuccessResponse]):
    root: JSONRPCErrorResponse | SendMessageSuccessResponse
    """
    JSON-RPC response model for the 'message/send' method.
    """


class SendStreamingMessageResponse(
    RootModel[JSONRPCErrorResponse | SendStreamingMessageSuccessResponse]
):
    root: JSONRPCErrorResponse | SendStreamingMessageSuccessResponse
    """
    JSON-RPC response model for the 'message/stream' method.
    """
