"""
Base A2A Agent Wrapper
Simplified base class for A2A agents
"""

from abc import ABC, abstractmethod
from typing import List, AsyncIterable
import logging
import asyncio

from crewai import Agent
from .types import (
    AgentCard, AgentCapabilities, AgentSkill, AgentProvider,
    SendTaskRequest, SendTaskResponse, TaskStatus, TaskState,
    Message, TextPart, SendTaskStreamingRequest, SendTaskStreamingResponse,
    TaskStatusUpdateEvent
)
from ..server.task_manager import InMemoryTaskManager
from .config import A2AConfig


class BaseA2AAgent(ABC):
    """
    Base class for all A2A agent wrappers
    Provides common interface to wrap CrewAI agents into A2A protocol
    """
    
    def __init__(self, agent_name: str, crewai_agent: Agent = None):
        self.agent_name = agent_name
        self.crewai_agent = crewai_agent
        self.logger = logging.getLogger(f"A2A.{agent_name}")
        self.config = A2AConfig.get_agent_config(agent_name)
        
        # Task manager to manage A2A tasks
        self.task_manager = A2ATaskManager(self)
        
        # Agent card for A2A protocol
        self.agent_card = self._create_agent_card()
        
        self.logger.info(f"Initialized A2A agent: {agent_name}")

    @abstractmethod
    def get_agent_description(self) -> str:
        """Return agent description"""
        pass

    @abstractmethod
    def get_agent_skills(self) -> List[AgentSkill]:
        """Return list of agent skills"""
        pass

    @abstractmethod
    async def process_task(self, task_content: str, session_id: str = None) -> str:
        """Process task and return result"""
        pass

    def _create_agent_card(self) -> AgentCard:
        """Create agent card for A2A protocol"""
        return AgentCard(
            name=self.config.name,
            description=self.get_agent_description(),
            url=A2AConfig.get_agent_url(self.agent_name),
            provider=AgentProvider(
                organization=self.config.organization,
                url=A2AConfig.get_base_url()
            ),
            version=self.config.version,
            capabilities=AgentCapabilities(
                streaming=True,
                pushNotifications=False,
                stateTransitionHistory=True
            ),
            defaultInputModes=A2AConfig.SUPPORTED_INPUT_MODES,
            defaultOutputModes=A2AConfig.SUPPORTED_OUTPUT_MODES,
            skills=self.get_agent_skills()
        )

    def get_agent_card(self) -> AgentCard:
        """Return agent card"""
        return self.agent_card

    async def handle_a2a_task(self, task_content: str, session_id: str = None) -> str:
        """
        Process A2A task and return result
        This is the main method called from A2A protocol
        """
        try:
            self.logger.info(f"Processing A2A task: {task_content[:100]}...")
            result = await self.process_task(task_content, session_id)
            self.logger.info(f"Task completed successfully")
            return result
        except Exception as e:
            self.logger.error(f"Error processing task: {str(e)}")
            raise


class A2ATaskManager(InMemoryTaskManager):
    """
    Custom Task Manager for A2A agents
    Connects A2A protocol with CrewAI agents
    """
    
    def __init__(self, a2a_agent: BaseA2AAgent):
        super().__init__()
        self.a2a_agent = a2a_agent
        self.logger = logging.getLogger(f"A2A.TaskManager.{a2a_agent.agent_name}")

    async def on_send_task(self, request: SendTaskRequest) -> SendTaskResponse:
        """Process send task request"""
        self.logger.info(f"Received send task request: {request.params.id}")
        
        try:
            # Upsert task into store
            task = await self.upsert_task(request.params)
            
            # Update status to WORKING
            await self.update_store(
                task.id,
                TaskStatus(state=TaskState.WORKING),
                []
            )
            
            # Get task content from message
            task_content = ""
            for part in request.params.message.parts:
                if part.type == "text":
                    task_content += part.text + " "
            
            # Process task using A2A agent
            result = await self.a2a_agent.handle_a2a_task(
                task_content.strip(), 
                request.params.sessionId
            )
            
            # Create response message
            response_message = Message(
                role="agent",
                parts=[TextPart(text=result)]
            )
            
            # Update status to COMPLETED
            completed_task = await self.update_store(
                task.id,
                TaskStatus(state=TaskState.COMPLETED, message=response_message),
                []
            )
            
            return SendTaskResponse(id=request.id, result=completed_task)
            
        except Exception as e:
            self.logger.error(f"Error processing send task: {str(e)}")
            
            # Update status to FAILED
            error_message = Message(
                role="agent",
                parts=[TextPart(text=f"Error: {str(e)}")]
            )
            
            failed_task = await self.update_store(
                request.params.id,
                TaskStatus(state=TaskState.FAILED, message=error_message),
                []
            )
            
            return SendTaskResponse(id=request.id, result=failed_task)

    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse]:
        """Process streaming task request"""
        self.logger.info(f"Received streaming task request: {request.params.id}")
        
        # Setup SSE consumer
        sse_queue = await self.setup_sse_consumer(request.params.id)
        
        # Process task in background
        asyncio.create_task(self._process_streaming_task(request))
        
        # Stream events
        async for response in self.dequeue_events_for_sse(
            request.id, request.params.id, sse_queue
        ):
            yield response

    async def _process_streaming_task(self, request: SendTaskStreamingRequest):
        """Process streaming task in background"""
        try:
            # Upsert task
            task = await self.upsert_task(request.params)
            
            # Send WORKING event
            working_event = TaskStatusUpdateEvent(
                id=task.id,
                status=TaskStatus(state=TaskState.WORKING),
                final=False
            )
            await self.enqueue_events_for_sse(task.id, working_event)
            
            # Get task content
            task_content = ""
            for part in request.params.message.parts:
                if part.type == "text":
                    task_content += part.text + " "
            
            # Process task
            result = await self.a2a_agent.handle_a2a_task(
                task_content.strip(),
                request.params.sessionId
            )
            
            # Send COMPLETED event
            response_message = Message(
                role="agent",
                parts=[TextPart(text=result)]
            )
            
            completed_event = TaskStatusUpdateEvent(
                id=task.id,
                status=TaskStatus(state=TaskState.COMPLETED, message=response_message),
                final=True
            )
            await self.enqueue_events_for_sse(task.id, completed_event)
            
        except Exception as e:
            self.logger.error(f"Error in streaming task: {str(e)}")
            
            # Send FAILED event
            error_message = Message(
                role="agent", 
                parts=[TextPart(text=f"Error: {str(e)}")]
            )
            
            failed_event = TaskStatusUpdateEvent(
                id=request.params.id,
                status=TaskStatus(state=TaskState.FAILED, message=error_message),
                final=True
            )
            await self.enqueue_events_for_sse(request.params.id, failed_event)
