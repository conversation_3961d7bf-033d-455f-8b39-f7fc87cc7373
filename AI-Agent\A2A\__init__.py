"""
A2A (Agent-to-Agent) Protocol Integration
Consolidated A2A components cho hệ thống AI-Agent
"""

# Core A2A Components
from .agents.types import *
from .agents.config import A2AConfig

# A2A Agents
from .agents.base_a2a_agent import BaseA2AAgent
from .agents.dynamic_agent_factory import DynamicAgentFactory, DynamicA2AAgent

# A2A Server
from .server.server import A2AServer
from .server.task_manager import <PERSON><PERSON>anager, InMemoryTaskManager
from .server.multi_agent_server import MultiAgentServer

__version__ = "1.0.0"
__all__ = [
    # Types & Config
    'A2AConfig',

    # Agents
    'BaseA2AAgent', 'DynamicAgentFactory', 'DynamicA2AAgent',

    # Server
    'A2AServer', 'TaskManager', 'InMemoryTaskManager',
    'MultiAgentServer'
]
