#!/usr/bin/env python3
"""
Multi Agents System - Interactive Chat Interface
Main entry point with server startup and chat interface
"""

import asyncio
import sys
import threading
import time
import requests
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to reduce noise
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.WARNING)
logging.getLogger("litellm").setLevel(logging.WARNING)

def print_banner():
    """Print system banner"""
    banner = """
╔════════════════════════════════════════════════════════════╗
║                    MULTI AGENTS SYSTEM                     ║
║                  Interactive Chat Interface                ║
╠════════════════════════════════════════════════════════════╣
║  🔧 Execution Agent    : http://localhost:5001             ║
║  📋 Planner Agent      : http://localhost:5002             ║
║  🔬 Researcher Agent   : http://localhost:5003             ║
║  ✍️ Writer Agent        : http://localhost:5004             ║
║  📊 Analyst Agent      : http://localhost:5005             ║
╠════════════════════════════════════════════════════════════╣
║  🌐 A2A Protocol Endpoints:                                ║
║     /.well-known/agent.json  - Agent Card                  ║
║     /a2a/task                - Task Processing             ║
║     /health                  - Health Check                ║
║     /info                    - Agent Information           ║
╚════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking environment...")

    # Check database connection
    try:
        from database.postgresql import get_db_connection
        with get_db_connection() as conn:
            print("✅ Database connection: OK")
    except Exception as e:
        print(f"❌ Database connection: FAILED - {str(e)}")
        return False

    # Check agents in database
    try:
        from A2A.agents.dynamic_agent_factory import DynamicAgentFactory
        factory = DynamicAgentFactory()
        agents = factory.get_available_agents()
        print(f"✅ Available agents: {len(agents)} ({', '.join(agents)})")

        if len(agents) == 0:
            print("❌ No agents found in database!")
            return False

    except Exception as e:
        print(f"❌ Agent factory: FAILED - {str(e)}")
        return False

    print("✅ Environment check passed!")
    return True

def start_server_background():
    """Start Multi Agent Server in background thread"""
    def run_server():
        try:
            from A2A.server.multi_agent_server import main as run_multi_agent_server
            asyncio.run(run_multi_agent_server())
        except Exception as e:
            print(f"❌ Server error: {e}")
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    return server_thread

def wait_for_server_ready(timeout=60):
    """Wait for server to be ready with better error handling"""
    print("⏳ Waiting for agents to be ready...")
    start_time = time.time()
    last_error = None

    for attempt in range(timeout):
        try:
            response = requests.get("http://localhost:5001/health", timeout=3)
            if response.status_code == 200:
                print("✅ All agents are ready!")
                return True
        except Exception as e:
            last_error = str(e)
            if attempt % 10 == 0:  # Print status every 10 seconds
                print(f"⏳ Still waiting... (attempt {attempt+1}/{timeout})")

        time.sleep(0.1)  # Reduced from 1s to 0.1s for faster response

        # Check if we've exceeded timeout
        if time.time() - start_time > timeout:
            break

    print(f"❌ Timeout waiting for agents to be ready. Last error: {last_error}")
    return False

def process_user_input(user_input: str) -> dict:
    """Process user input through the Multi Agent System"""
    try:
        start_time = time.time()
        
        # Send request to Execution Agent
        response = requests.post(
            "http://localhost:5001/a2a/task",
            json={
                'content': user_input,
                'session_id': f'chat-{int(time.time())}'
            },
            timeout=90  # Giảm từ 180s xuống 90s
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            return {
                'success': True,
                'result': result.get('result', 'No result'),
                'agent': result.get('agent', 'unknown'),
                'status': result.get('status', 'unknown'),
                'processing_time': processing_time
            }
        else:
            return {
                'success': False,
                'error': f"HTTP {response.status_code}: {response.text}",
                'processing_time': processing_time
            }
            
    except requests.exceptions.Timeout:
        return {
            'success': False,
            'error': "Request timeout (task took longer than 3 minutes)",
            'processing_time': 180
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Error: {str(e)}",
            'processing_time': 0
        }

def print_result(result: dict):
    """Print formatted result"""
    print("\n" + "─" * 70)
    
    if result['success']:
        print(f"✅ TASK COMPLETED SUCCESSFULLY")
        print(f"⏱️  Processing Time: {result['processing_time']:.2f} seconds")
        print(f"🤖 Processed by: {result['agent']} agent")
        print(f"📊 Status: {result['status']}")
        print("\n📋 RESULT:")
        print("─" * 70)
        print(result['result'])
    else:
        print(f"❌ TASK FAILED")
        print(f"⏱️  Processing Time: {result['processing_time']:.2f} seconds")
        print(f"🚫 Error: {result['error']}")
        
    print("─" * 70)

def run_chat_interface():
    """Run the interactive chat interface"""
    print("\n🎯 Multi Agents System is ready!")
    print("💬 Type your questions or tasks below.")
    print("📝 Examples:")
    print("   • What is artificial intelligence?")
    print("   • Create a business plan for an AI startup")
    print("   • Analyze the future of blockchain technology")
    print("🔄 Type 'exit' or 'quit' to stop the system")
    print("=" * 70)
    
    while True:
        try:
            # Get user input
            user_input = input("\n💭 You: ").strip()
            
            # Check for exit commands
            if user_input.lower() in ['exit', 'quit', 'bye', 'stop']:
                print("\n👋 Goodbye! Shutting down Multi Agents System...")
                break
                
            # Skip empty input
            if not user_input:
                continue
                
            # Show processing indicator
            print(f"\n🤔 Processing: '{user_input[:50]}{'...' if len(user_input) > 50 else ''}'")
            print("⏳ Please wait...")
            
            # Process the input
            result = process_user_input(user_input)
            
            # Display result
            print_result(result)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Shutting down Multi Agents System...")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")

def main():
    """Main entry point"""
    try:
        # Print banner
        print_banner()
        
        # Check environment
        if not check_environment():
            print("\n❌ Environment check failed. Please fix the issues above.")
            sys.exit(1)
        
        print("\n🚀 Starting Multi Agents System...")
        
        # Start server in background
        server_thread = start_server_background()
        
        # Wait for server to be ready with extended timeout
        if not wait_for_server_ready(timeout=90):
            print("❌ Failed to start agents. Exiting...")
            print("💡 Try running 'docker-compose logs multi-agents-system' to see detailed logs")
            sys.exit(1)
        
        # Run interactive chat interface
        run_chat_interface()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down Multi Agents System...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("✅ Multi Agents System stopped. Goodbye!")

if __name__ == "__main__":
    main()
