"""
A2A Configuration
Configuration for A2A protocol integration
"""

import os
from typing import Dict, List
from dataclasses import dataclass


@dataclass
class A2AServerConfig:
    """Configuration for A2A Server"""
    host: str = "0.0.0.0"
    port: int = 5000
    endpoint: str = "/"
    agent_card_path: str = "/.well-known/agent.json"


@dataclass
class A2AAgentConfig:
    """Configuration for A2A Agent"""
    name: str
    description: str
    version: str = "1.0.0"
    organization: str = "AI-Agent"
    server_config: A2AServerConfig = None
    
    def __post_init__(self):
        if self.server_config is None:
            self.server_config = A2AServerConfig()


class A2AConfig:
    """
    Main configuration for A2A integration
    Manages configuration for all A2A agents and servers
    """
    
    # Default ports for different agents
    DEFAULT_PORTS = {
        "execution": 5001,
        "planner": 5002,
        "orchestrator": 5003
    }
    
    # Supported content types
    SUPPORTED_INPUT_MODES = ["text", "text/plain"]
    SUPPORTED_OUTPUT_MODES = ["text", "text/plain", "application/json"]
    
    @classmethod
    def get_agent_config(cls, agent_name: str) -> A2AAgentConfig:
        """Get configuration for specific agent"""
        port = cls.DEFAULT_PORTS.get(agent_name, 5000)
        
        configs = {
            "execution": A2AAgentConfig(
                name="execution-agent",
                description="Main agent for communicating with users and coordinating other agents",
                server_config=A2AServerConfig(port=port)
            ),
            "planner": A2AAgentConfig(
                name="planner-agent", 
                description="Agent for planning and analyzing complex tasks",
                server_config=A2AServerConfig(port=port)
            ),
            "orchestrator": A2AAgentConfig(
                name="orchestrator-agent",
                description="Agent for orchestrating and managing workflow between agents",
                server_config=A2AServerConfig(port=port)
            )
        }
        
        return configs.get(agent_name, A2AAgentConfig(
            name=agent_name,
            description=f"A2A Agent: {agent_name}",
            server_config=A2AServerConfig(port=port)
        ))
    
    @classmethod
    def get_all_agent_urls(cls) -> Dict[str, str]:
        """Get URLs of all A2A agents"""
        urls = {}
        for agent_name, port in cls.DEFAULT_PORTS.items():
            urls[agent_name] = f"http://localhost:{port}"
        return urls
    
    @classmethod
    def get_agent_url(cls, agent_name: str) -> str:
        """Get URL of specific agent"""
        port = cls.DEFAULT_PORTS.get(agent_name, 5000)

        # Check if running in Docker
        base_url = cls.get_base_url()
        if "localhost" in base_url:
            # If there is an environment variable for specific agent URL, use it
            env_var = f"A2A_{agent_name.upper()}_URL"
            agent_url = os.getenv(env_var)
            if agent_url:
                return agent_url

            # Fallback to localhost
            return f"http://localhost:{port}"
        else:
            # Use container name in Docker
            container_name = f"a2a-{agent_name}-agent"
            return f"http://{container_name}:{port}"
    
    @classmethod
    def get_base_url(cls) -> str:
        """Get base URL for A2A services"""
        return os.getenv("A2A_BASE_URL", "http://localhost")
    
    @classmethod
    def is_a2a_enabled(cls) -> bool:
        """Check if A2A is enabled"""
        return os.getenv("A2A_ENABLED", "true").lower() == "true"
    
    @classmethod
    def get_timeout(cls) -> int:
        """Get timeout for A2A requests"""
        return int(os.getenv("A2A_TIMEOUT", "30"))


# Environment variables for A2A
A2A_ENV_VARS = {
    "A2A_ENABLED": "true",
    "A2A_BASE_URL": "http://localhost", 
    "A2A_TIMEOUT": "30",
    "A2A_LOG_LEVEL": "INFO"
}
