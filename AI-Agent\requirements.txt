# ======================================================================
#  AI-Agent Project Dependencies - WITH A2A Protocol Integration
#  CrewAI + A2A Protocol Support
# ======================================================================

# ─────────── Core web runtimes ─────────────────────────────────────────
fastapi
uvicorn[standard]
httpx
streamlit>=1.32.0

# ─────────── CrewAI Framework ──────────────────────────────────────────
crewai[tools]==0.126.0

# ─────────── Basic Dependencies ───────────────────────────────────────
pydantic
typing-extensions

# ─────────── LLM Integration ───────────────────────────────────────────
openai
anthropic

# ─────────── Database & Storage ────────────────────────────────────────
psycopg2-binary
sqlalchemy

# ─────────── Utilities & Config ───────────────────────────────────────
python-dotenv

# ─────────── A2A Protocol Dependencies ────────────────────────────────
httpx-sse>=0.4.0
sse-starlette>=2.2.1
starlette>=0.46.1
jwcrypto>=1.5.6
pyjwt>=2.10.1

# ─────────── Development & Testing ────────────────────────────────────
pytest
black
