"""
Chat UI components for Streamlit
"""

import streamlit as st
from typing import List, Dict, Any

def apply_custom_css():
    """Apply custom CSS for chat interface"""
    st.markdown("""
        <style>
        .stChatInput>div>div>input {
            border-radius: 20px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-content {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 100px);
            padding-bottom: 80px;
        }
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 90px;
        }
        .input-container {
            position: fixed;
            bottom: 0;
            left: 25%;
            width: 74%;
            padding: 10px 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #eee;
            z-index: 100;
        }
        </style>
    """, unsafe_allow_html=True)

def display_banner():
    """Display system banner"""
    # Tạo container với style tùy chỉnh
    banner_container = st.container()
    
    with banner_container:
        # Tiêu đề
        col1, col2, col3 = st.columns([1, 3, 1])
        with col2:
            st.title("MULTI AGENTS SYSTEM", anchor=False)
            st.subheader("AI-Powered Assistant", anchor=False)
        
        st.divider()
        
        # Display agent icons
        cols = st.columns(5)
        
        with cols[0]:
            st.markdown("<div style='text-align: center;'><span style='font-size: 24px;'>🔧</span></div>", unsafe_allow_html=True)
            st.markdown("<div style='text-align: center;'><b>Execution</b></div>", unsafe_allow_html=True)
            
        with cols[1]:
            st.markdown("<div style='text-align: center;'><span style='font-size: 24px;'>📋</span></div>", unsafe_allow_html=True)
            st.markdown("<div style='text-align: center;'><b>Planning</b></div>", unsafe_allow_html=True)
            
        with cols[2]:
            st.markdown("<div style='text-align: center;'><span style='font-size: 24px;'>🔬</span></div>", unsafe_allow_html=True)
            st.markdown("<div style='text-align: center;'><b>Research</b></div>", unsafe_allow_html=True)
            
        with cols[3]:
            st.markdown("<div style='text-align: center;'><span style='font-size: 24px;'>✍️</span></div>", unsafe_allow_html=True)
            st.markdown("<div style='text-align: center;'><b>Writing</b></div>", unsafe_allow_html=True)
            
        with cols[4]:
            st.markdown("<div style='text-align: center;'><span style='font-size: 24px;'>📊</span></div>", unsafe_allow_html=True)
            st.markdown("<div style='text-align: center;'><b>Analysis</b></div>", unsafe_allow_html=True)
        
        # Footer
        st.markdown("<div style='text-align: center; color: #616161; font-style: italic; margin-top: 20px;'>Powered by AI Agent-to-Agent (A2A) Protocol</div>", unsafe_allow_html=True)

def render_chat_messages():
    """Render chat messages from session state"""
    # Create a container for chat history with custom class
    st.markdown('<div class="main-content">', unsafe_allow_html=True)
    st.markdown('<div class="chat-container">', unsafe_allow_html=True)
    
    # Chat container for messages
    chat_container = st.container()
    
    with chat_container:
        # Display existing messages
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
                
                # Show if memory was used
                if message["role"] == "assistant" and message.get("memory_used", False):
                    st.caption("🧠 Memory enhanced response")
        
        # Display processing status if active
        if st.session_state.processing:
            with st.chat_message("assistant"):
                st.write("Thinking...")
    
    # Close chat container div
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Create a fixed container at the bottom for the input
    st.markdown('<div class="input-container">', unsafe_allow_html=True)
    
    # Handle new user input
    user_input = st.chat_input("Ask a question or provide a task", key="chat_input")
    
    st.markdown('</div>', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)
    
    return user_input

def render_chat_interface():
    """Render the complete chat interface"""
    # Title
    st.title("🤖 Multi Agents System")
    
    # Apply custom CSS
    apply_custom_css()
    
    # Render chat messages and get user input
    user_input = render_chat_messages()
    
    return user_input 