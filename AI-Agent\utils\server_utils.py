"""
Server utilities for Multi Agent System
"""

import streamlit as st
import time
import logging
import threading
import asyncio
import requests
from pathlib import Path

# Thay thế import tương đối bằng import tuyệt đối
from A2A.server.multi_agent_server import main as run_multi_agent_server
from A2A.agents.dynamic_agent_factory import DynamicAgentFactory
from database.postgresql import get_db_connection

# Global variables
server_thread = None
server_started = False

def check_environment():
    """Check if environment is properly set up"""
    # Create placeholders for messages
    status_placeholder = st.empty()
    db_placeholder = st.empty()
    agents_placeholder = st.empty()
    result_placeholder = st.empty()
    
    # Display checking message
    status_placeholder.info("🔍 Checking environment...")
    
    status_ok = True
    
    # Check database connection
    try:
        with get_db_connection() as conn:
            db_placeholder.success("✅ Database connection: OK")
    except Exception as e:
        db_placeholder.error(f"❌ Database connection: FAILED - {str(e)}")
        status_ok = False
    
    # Check agents in database
    try:
        factory = DynamicAgentFactory()
        agents = factory.get_available_agents()
        agents_placeholder.success(f"✅ Available agents: {len(agents)} ({', '.join(agents)})")
        
        if len(agents) == 0:
            agents_placeholder.error("❌ No agents found in database!")
            status_ok = False
            
    except Exception as e:
        agents_placeholder.error(f"❌ Agent factory: FAILED - {str(e)}")
        status_ok = False
    
    if status_ok:
        result_placeholder.success("✅ Environment check passed!")
    
    # Clear all messages
    status_placeholder.empty()
    db_placeholder.empty()
    agents_placeholder.empty()
    result_placeholder.empty()
    
    return status_ok

def start_server_background():
    """Start Multi Agent Server in background thread"""
    def run_server():
        try:
            asyncio.run(run_multi_agent_server())
        except Exception as e:
            st.error(f"❌ Server error: {e}")
    
    global server_thread
    if server_thread is None:
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
    
    return server_thread

def wait_for_server_ready(timeout=60, progress_bar=None):
    """Wait for server to be ready with better error handling"""
    start_time = time.time()
    last_error = None
    
    for attempt in range(timeout):
        try:
            response = requests.get("http://localhost:5001/health", timeout=3)
            if response.status_code == 200:
                if progress_bar:
                    progress_bar.progress(1.0)
                return True
        except Exception as e:
            last_error = str(e)
        
        time.sleep(0.1)  # Reduced from 1s to 0.1s for faster response
        if progress_bar:
            progress_bar.progress((attempt + 1) / timeout)
            
        # Check if we've exceeded timeout
        if time.time() - start_time > timeout:
            break
    
    if progress_bar:
        progress_bar.progress(1.0)
    return False

def initialize_server():
    """Initialize server when app starts"""
    if 'server_initialized' not in st.session_state:
        st.session_state.server_initialized = False
        
    if not st.session_state.server_initialized:
        # Check environment
        env_ok = check_environment()
        if env_ok:
            # Create placeholders for messages and progress bar
            spinner_placeholder = st.empty()
            progress_placeholder = st.empty()
            result_placeholder = st.empty()
            
            with spinner_placeholder.container():
                st.spinner("Starting server...")
                
                # Start server in background
                start_server_background()
                
                # Wait for server to be ready
                progress_bar = progress_placeholder.progress(0)
                server_ready = wait_for_server_ready(timeout=90, progress_bar=progress_bar)
                
                if server_ready:
                    st.session_state.server_started = True
                    result_placeholder.success("✅ All agents are ready!")
                else:
                    result_placeholder.error("❌ Failed to start agents.")
                    result_placeholder.info("💡 Check logs for more details.")
            
            # Clear messages after completion
            spinner_placeholder.empty()
            progress_placeholder.empty()
            result_placeholder.empty()
        
        st.session_state.server_initialized = True 