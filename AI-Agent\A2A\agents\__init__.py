"""
A2A Agents
A2A agent wrappers for AI-Agent system
"""

# A2A Agents - Import is enabled after dependencies are ready
try:
    from .base_a2a_agent import BaseA2AAgent
    from .dynamic_agent_factory import DynamicA2AAgent, DynamicAgentFactory

    __all__ = ['BaseA2AAgent', 'DynamicA2AAgent', 'DynamicAgentFactory']
except ImportError as e:
    # Graceful fallback if dependencies are not yet ready
    import logging
    logging.getLogger("A2A.Import").warning(f"A2A agents not available: {e}")
    __all__ = []
