"""
Dynamic Agent Factory for Multi Agents System
Factory to create A2A agents from database records instead of hard-coded classes
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from crewai import Agent

from .base_a2a_agent import BaseA2AAgent
from .types import <PERSON><PERSON><PERSON>, <PERSON><PERSON>apabilities, AgentSkill, Agent<PERSON><PERSON>ider
from .agent_templates import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AGENT_TYPE_TEMPLATES, DEFAULT_CAPABILITIES
from ..server.task_manager import InMemoryTaskManager


class DynamicA2AAgent(BaseA2AAgent):
    """
    Dynamic A2A Agent created from database record
    Replaces static agent classes like ExecutionA2AAgent, PlannerA2AAgent
    """
    
    def __init__(self, agent_data: Dict[str, Any]):
        import logging
        self.logger = logging.getLogger(f"DynamicA2AAgent-{agent_data.get('name', 'unknown')}")

        self.logger.info(f"Initializing DynamicA2AAgent: {agent_data.get('name')}")

        self.agent_data = agent_data
        self.agent_type = agent_data.get('agent_type', 'general')

        # Setup agent-specific configurations FIRST
        self._setup_agent_config()

        # Create CrewAI agent from data
        crewai_agent = self._create_crewai_agent(agent_data)

        # Initialize base A2A agent
        super().__init__(agent_data['name'], crewai_agent)

        self.logger.info(f"DynamicA2AAgent initialized successfully: {agent_data.get('name')}")
    
    def _create_crewai_agent(self, agent_data: Dict[str, Any]) -> Agent:
        """Create CrewAI agent from database data"""
        # Import global llm from Multi_Agent.crew
        from Multi_Agent.crew import llm

        # Create agent with data from database
        agent = Agent(
            role=agent_data['role'],
            goal=agent_data['goal'],
            backstory=agent_data['backstory'],
            llm=llm,  # Use global llm
            verbose=False,
            allow_delegation=False
        )

        return agent
    
    def _setup_agent_config(self):
        """Setup specific configuration for agent"""
        template = AgentTemplateManager.get_template(self.agent_type)
        
        # Setup port from database or template
        self.port = self.agent_data.get('port', template.get('default_port', 5000))
        
        # Setup priority
        self.priority = template.get('priority', 'medium')
    
    def get_agent_description(self) -> str:
        """Get agent description"""
        return self.agent_data['role']

    def get_agent_skills(self) -> List[AgentSkill]:
        """Get agent skills as AgentSkill objects"""
        template = AgentTemplateManager.get_template(self.agent_type)

        # Generate skills from template and auto-detection
        template_skills = template.get('skills', [])
        detected_skill_names = AgentTemplateManager.extract_skills_from_text(
            self.agent_data['role'],
            self.agent_data['goal']
        )

        # Combine template skills with detected skills
        skills = []

        # Add skills from template
        for skill_data in template_skills:
            # Ensure all required fields are present
            skill_dict = skill_data.copy()
            if 'examples' not in skill_dict:
                skill_dict['examples'] = []
            if 'inputModes' not in skill_dict:
                skill_dict['inputModes'] = ["text"]
            if 'outputModes' not in skill_dict:
                skill_dict['outputModes'] = ["text"]
            skills.append(AgentSkill(**skill_dict))

        # Add detected skills (if not already in template)
        existing_skill_ids = {skill_data['id'] for skill_data in template_skills}
        for skill_name in detected_skill_names:
            if skill_name not in existing_skill_ids:
                skills.append(AgentSkill(
                    id=skill_name,
                    name=skill_name.replace('_', ' ').title(),
                    description=f"Auto-detected {skill_name} capability",
                    tags=[skill_name],
                    examples=[],
                    inputModes=["text"],
                    outputModes=["text"]
                ))

        return skills

    async def handle_a2a_task(self, task_content: str, session_id: str = None) -> str:
        """Handle A2A task request - wrapper for process_task"""
        return await self.process_task(task_content, session_id)

    def _create_agent_card(self):
        """Override base method to use port from database"""
        from .types import AgentCard, AgentCapabilities, AgentProvider
        from .config import A2AConfig

        return AgentCard(
            name=self.config.name,
            description=self.get_agent_description(),
            url=f"http://localhost:{self.port}",  # Use port from database
            provider=AgentProvider(
                organization=self.config.organization,
                url=A2AConfig.get_base_url()
            ),
            version=self.config.version,
            capabilities=AgentCapabilities(
                streaming=True,
                pushNotifications=False,
                stateTransitionHistory=True
            ),
            defaultInputModes=A2AConfig.SUPPORTED_INPUT_MODES,
            defaultOutputModes=A2AConfig.SUPPORTED_OUTPUT_MODES,
            skills=self.get_agent_skills()
        )

    def _evaluate_task_complexity(self, task_content: str) -> str:
        """
        Evaluate task complexity by AI - Intelligent and multi-lingual
        Improved to better understand context and intent
        """
        try:
            # Import LLM
            from Multi_Agent.crew import llm

            # AI-based complexity evaluation prompt with better criteria
            evaluation_prompt = f"""
Analyze the following task and determine if it's SIMPLE or COMPLEX:

Task: "{task_content}"

Classification criteria:
SIMPLE tasks:
- Single question or request
- Basic information seeking (what, when, where, who)
- Simple explanations or definitions
- Quick answers (1-2 sentences)
- Basic greetings or introductions
- Simple yes/no questions
- Basic language practice or simple exercises
- Single action requests

COMPLEX tasks:
- Multiple requirements or questions
- Analysis, comparison, or evaluation needed
- Research or investigation required
- Comprehensive explanations or reports
- Strategic planning or problem-solving
- Creative tasks (writing, designing, planning)
- Multi-step processes
- Educational content generation
- Detailed instructions or tutorials
- Complex problem-solving

Examples:
SIMPLE: "What is AI?", "AI là gì?", "Hello", "How are you?", "What time is it?", "Give me one question", "I need 1 question"
COMPLEX: "Create a business plan", "Analyze market trends", "Write a comprehensive report", "Design a system", "Create multiple questions", "Generate a test"

Respond with ONLY one word: "simple" or "complex"
"""

            # Get AI evaluation using CrewAI LLM.call method
            response = llm.call(evaluation_prompt)
            ai_result = str(response).lower().strip()

            # Extract simple/complex from response
            if 'simple' in ai_result:
                ai_complexity = 'simple'
            elif 'complex' in ai_result:
                ai_complexity = 'complex'
            else:
                # Fallback to rule-based if AI response is unclear
                ai_complexity = self._fallback_complexity_evaluation(task_content)

            # Combine AI evaluation with rule-based validation
            rule_complexity = self._fallback_complexity_evaluation(task_content)

            # Final decision logic with improved heuristics
            if ai_complexity == rule_complexity:
                # Both agree
                final_complexity = ai_complexity
            else:
                # Disagreement - use improved heuristics
                final_complexity = self._resolve_complexity_disagreement(
                    task_content, ai_complexity, rule_complexity
                )

            self.logger.info(f"Complexity evaluation: AI={ai_complexity}, Rule={rule_complexity}, Final={final_complexity}")
            return final_complexity

        except Exception as e:
            self.logger.warning(f"AI complexity evaluation failed: {e}, using fallback")
            return self._fallback_complexity_evaluation(task_content)

    def _resolve_complexity_disagreement(self, task_content: str, ai_complexity: str, rule_complexity: str) -> str:
        """
        Resolve disagreement between AI and rule-based complexity evaluation
        """
        # Check for specific patterns that indicate complexity
        task_lower = task_content.lower()
        
        # Simple indicators
        simple_indicators = [
            "hello", "hi", "xin chào", "chào", "how are you", "bạn khỏe không",
            "what is", "what's", "là gì", "what time", "mấy giờ",
            "one", "1", "một", "single", "duy nhất",
            "simple", "đơn giản", "basic", "cơ bản",
            "quick", "nhanh", "fast", "mau",
            "just", "chỉ", "only", "thôi"
        ]
        
        # Complex indicators
        complex_indicators = [
            "create", "tạo", "generate", "sinh", "make", "làm", "build", "xây dựng",
            "analyze", "phân tích", "research", "nghiên cứu", "study", "học",
            "multiple", "nhiều", "several", "một số", "comprehensive", "toàn diện",
            "detailed", "chi tiết", "extensive", "sâu rộng",
            "plan", "kế hoạch", "strategy", "chiến lược", "design", "thiết kế",
            "develop", "phát triển", "implement", "triển khai",
            "report", "báo cáo", "analysis", "phân tích", "framework", "khung",
            "system", "hệ thống", "proposal", "đề xuất",
            "questions", "câu hỏi", "test", "bài kiểm tra", "practice", "luyện tập",
            "exercise", "bài tập", "tutorial", "hướng dẫn",
            "compare", "so sánh", "evaluate", "đánh giá", "explain", "giải thích",
            "describe", "mô tả", "write", "viết", "compose", "soạn"
        ]
        
        # Special cases that should be complex regardless of other factors
        complex_special_cases = [
            "test khả năng", "test ability", "kiểm tra khả năng",
            "một vài câu hỏi", "several questions", "multiple questions",
            "nhiều câu hỏi", "various questions", "different questions",
            "system architecture", "kiến trúc hệ thống",
            "business plan", "kế hoạch kinh doanh",
            "market analysis", "phân tích thị trường",
            "comprehensive", "toàn diện", "detailed", "chi tiết",
            "gửi cho tôi", "send me", "give me", "cho tôi",
            "để tôi kiểm tra", "for me to check", "for me to test"
        ]
        
        # Special cases that should be simple regardless of other factors
        simple_special_cases = [
            "test", "kiểm tra", "check", "xem", "look",
            "hello", "hi", "xin chào", "chào",
            "one question", "một câu hỏi", "single question",
            "just one", "chỉ một", "only one",
            "medium length", "trung bình", "average length"
        ]
        
        # Check special cases first
        for case in complex_special_cases:
            if case in task_lower:
                return 'complex'
        
        for case in simple_special_cases:
            if case in task_lower:
                return 'simple'
        
        # Count indicators
        simple_count = sum(1 for indicator in simple_indicators if indicator in task_lower)
        complex_count = sum(1 for indicator in complex_indicators if indicator in task_lower)
        
        # Word count analysis
        word_count = len(task_content.split())
        
        # Decision logic
        if simple_count > complex_count:
            return 'simple'
        elif complex_count > simple_count:
            return 'complex'
        elif word_count <= 8:
            return 'simple'
        elif word_count >= 15:
            return 'complex'
        else:
            # For medium-length tasks, prefer AI evaluation
            return ai_complexity

    def _fallback_complexity_evaluation(self, task_content: str) -> str:
        """
        Rule-based fallback complexity evaluation - Multi-lingual
        Improved to better handle context and intent
        """
        task_lower = task_content.lower()
        
        # Check for specific simple patterns first (HIGH PRIORITY)
        simple_indicators = [
            # Greetings and basic interactions
            "hello", "hi", "xin chào", "chào", "how are you", "bạn khỏe không",
            
            # Single/one requests
            "one", "1", "một", "single", "duy nhất", "chỉ", "only", "just",
            "tôi chỉ cần", "i only need", "just one", "chỉ một",
            
            # Basic questions
            "what is", "what's", "là gì", "what time", "mấy giờ",
            "how to", "làm thế nào", "why", "tại sao",
            
            # Simple requests
            "simple", "đơn giản", "basic", "cơ bản", "quick", "nhanh",
            "brief", "ngắn gọn", "short", "ngắn",
            
            # Basic actions
            "tell me", "cho biết", "show me", "chỉ cho", "give me", "cho tôi"
        ]
        
        # Check for specific complex patterns (HIGH PRIORITY)
        complex_indicators = [
            # Creation and generation
            "create", "tạo", "generate", "sinh", "make", "làm", "build", "xây dựng",
            
            # Analysis and research
            "analyze", "phân tích", "research", "nghiên cứu", "study", "học",
            "investigate", "điều tra", "explore", "khám phá",
            
            # Multiple/comprehensive
            "multiple", "nhiều", "several", "một số", "comprehensive", "toàn diện",
            "detailed", "chi tiết", "extensive", "sâu rộng",
            
            # Planning and strategy
            "plan", "kế hoạch", "strategy", "chiến lược", "design", "thiết kế",
            "develop", "phát triển", "implement", "triển khai",
            
            # Complex outputs
            "report", "báo cáo", "analysis", "phân tích", "framework", "khung",
            "system", "hệ thống", "proposal", "đề xuất",
            
            # Educational content
            "questions", "câu hỏi", "test", "bài kiểm tra", "practice", "luyện tập",
            "exercise", "bài tập", "tutorial", "hướng dẫn",
            
            # Complex tasks
            "compare", "so sánh", "evaluate", "đánh giá", "explain", "giải thích",
            "describe", "mô tả", "write", "viết", "compose", "soạn"
        ]
        
        # Count indicators
        simple_count = sum(1 for indicator in simple_indicators if indicator in task_lower)
        complex_count = sum(1 for indicator in complex_indicators if indicator in task_lower)
        
        # Special case: "I only need one question" or similar
        if any(phrase in task_lower for phrase in ["chỉ cần", "only need", "just need", "tôi chỉ cần"]):
            if any(word in task_lower for word in ["1", "one", "một", "câu hỏi", "question"]):
                return 'simple'
        
        # Special case: Greetings and basic interactions
        if any(greeting in task_lower for greeting in ["hello", "hi", "xin chào", "chào", "how are you"]):
            return 'simple'
        
        # Decision logic
        if simple_count > complex_count:
            return 'simple'
        elif complex_count > simple_count:
            return 'complex'
        
        # Word count analysis (reduced weight)
        word_count = len(task_content.split())
        if word_count <= 5:
            return 'simple'
        elif word_count >= 20:
            return 'complex'
        elif word_count >= 12:
            return 'complex'
        else:
            # Default to simple for medium-length tasks
            return 'simple'

    async def process_task(self, task_content: str, session_id: str = None) -> str:
        """
        Process task with complete workflow delegation
        """
        self.logger.info(f"Dynamic Agent [{self.agent_data['name']}] processing task: {task_content[:100]}...")

        try:
            # EXECUTION AGENT - Entry Point & Delegation Logic
            if self.agent_data['agent_type'] == "execution":
                return await self._handle_execution_workflow(task_content, session_id)

            # PLANNER AGENT - Task Coordination & Result Synthesis
            elif self.agent_data['agent_type'] == "planner":
                return await self._handle_planner_workflow(task_content, session_id)

            # SPECIALIZED AGENTS - Direct Processing
            else:
                return await self._handle_specialized_agent(task_content, session_id)

        except Exception as e:
            self.logger.error(f"Error processing task in {self.agent_data['name']}: {str(e)}")
            return f"Error: {str(e)}"

    async def _handle_execution_workflow(self, task_content: str, session_id: str = None) -> str:
        """Handle Execution Agent workflow - Entry point & delegation"""
        complexity = self._evaluate_task_complexity(task_content)
        self.logger.info(f"Execution Agent evaluated task complexity: {complexity}")

        if complexity == "simple":
            # Handle simple task directly
            self.logger.info("Handling simple task directly...")
            return await self._process_simple_task(task_content, session_id)
        else:
            # Delegate complex task to Planner Agent
            self.logger.info("Delegating complex task to Planner Agent...")
            return await self._delegate_to_planner(task_content, session_id)

    async def _handle_planner_workflow(self, task_content: str, session_id: str = None) -> str:
        """Handle Planner Agent workflow - Task coordination & synthesis"""
        self.logger.info("Planner Agent breaking down complex task...")

        # 1. Analyze and breakdown task
        subtasks = await self._breakdown_task(task_content)

        # 2. Delegate to specialized agents
        results = await self._delegate_to_specialists(subtasks, session_id)

        # 3. Synthesize results
        final_result = await self._synthesize_results(task_content, results)

        return final_result

    async def _handle_specialized_agent(self, task_content: str, session_id: str = None) -> str:
        """Handle specialized agent processing"""
        self.logger.info(f"Specialized Agent [{self.agent_data['agent_type']}] processing subtask...")

        # Use CrewAI for specialized processing
        return await self._process_with_crewai(task_content)

    async def _process_simple_task(self, task_content: str, session_id: str = None) -> str:
        """Process simple task directly with CrewAI"""
        self.logger.info("Processing simple task directly...")
        return await self._process_with_crewai(task_content)

    async def _delegate_to_planner(self, task_content: str, session_id: str = None) -> str:
        """Delegate complex task to Planner Agent via A2A protocol"""
        try:
            import httpx

            planner_url = "http://localhost:5002/a2a/task"
            task_data = {
                'content': task_content,
                'session_id': session_id or f"exec-{id(self)}"
            }

            self.logger.info(f"Delegating to Planner Agent: {planner_url}")

            # Increase timeout for complex workflows - optimized for faster processing
            # Total time needed: 3 agents * (30s processing) + synthesis + buffer = ~120s
            timeout = httpx.Timeout(300.0, connect=20.0, read=300.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                self.logger.info(f"Sending request to Planner Agent with {len(str(task_data))} chars of data")
                response = await client.post(planner_url, json=task_data)

                if response.status_code == 200:
                    result = response.json()
                    final_result = result.get('result', 'No result from Planner Agent')
                    self.logger.info(f"✅ Successfully received result from Planner Agent ({len(final_result)} chars)")
                    return final_result
                else:
                    self.logger.error(f"Planner delegation failed: {response.status_code} - {response.text}")
                    return f"Error delegating to Planner Agent: {response.status_code}"

        except httpx.TimeoutException as e:
            self.logger.error(f"Timeout delegating to Planner after 300s: {str(e)}")
            self.logger.info("Attempting fallback: processing task directly without delegation")
            # Fallback: process directly without delegation
            try:
                fallback_result = await self._process_with_crewai_async(task_content)
                return f"⚠️ Planner Agent timeout - processed directly:\n\n{fallback_result}"
            except Exception as fallback_error:
                self.logger.error(f"Fallback processing failed: {fallback_error}")
                return f"Timeout communicating with Planner Agent after 5 minutes. The task may be too complex or the Planner Agent is overloaded."
        except Exception as e:
            self.logger.error(f"Error delegating to Planner: {str(e)}")
            return f"Error communicating with Planner Agent: {str(e)}"

    async def _breakdown_task(self, task_content: str) -> dict:
        """Break down complex task into subtasks for specialized agents"""
        self.logger.info("Breaking down complex task...")

        # Analyze task to determine which specialists are needed
        task_lower = task_content.lower()

        subtasks = {}

        # Research subtask
        if any(keyword in task_lower for keyword in ['research', 'find', 'investigate', 'analyze', 'study']):
            subtasks['researcher'] = f"Research and gather information about: {task_content}"

        # Writing subtask
        if any(keyword in task_lower for keyword in ['write', 'create', 'draft', 'compose', 'document']):
            subtasks['writer'] = f"Create written content for: {task_content}"

        # Analysis subtask
        if any(keyword in task_lower for keyword in ['analyze', 'evaluate', 'assess', 'compare', 'review']):
            subtasks['analyst'] = f"Analyze and provide insights on: {task_content}"

        # If no specific subtasks identified, assign to all specialists
        if not subtasks:
            subtasks = {
                'researcher': f"Research information about: {task_content}",
                'writer': f"Create comprehensive content about: {task_content}",
                'analyst': f"Analyze and provide insights on: {task_content}"
            }

        self.logger.info(f"Created {len(subtasks)} subtasks: {list(subtasks.keys())}")
        return subtasks

    async def _send_task_to_agent(self, url: str, task_content: str, agent_type: str, session_id: str = None) -> str:
        """Send task to an agent and get result"""
        import httpx

        task_data = {
            'content': task_content,
            'session_id': session_id or f"planner-{id(self)}"
        }

        self.logger.info(f"Delegating to {agent_type} agent: {url}")

        # Increase timeout for specialist processing
        timeout = httpx.Timeout(120.0, connect=20.0, read=120.0)
        async with httpx.AsyncClient(timeout=timeout) as client:
            self.logger.info(f"Sending request to {agent_type} agent with {len(str(task_data))} chars of data")
            response = await client.post(url, json=task_data)

            if response.status_code == 200:
                result = response.json()
                agent_result = result.get('result', f'No result from {agent_type}')
                return agent_result
            else:
                raise Exception(f"{agent_type} delegation failed: {response.status_code} - {response.text}")

    async def _delegate_to_specialists(self, subtasks: dict, session_id: str = None) -> dict:
        """Delegate subtasks to specialist agents in parallel"""
        self.logger.info(f"Created {len(subtasks)} subtasks: {list(subtasks.keys())}")
        
        # Get agent ports
        agent_ports = self._get_agent_ports()
        self.logger.info(f"Dynamic agent ports: {agent_ports}")
        
        # Create tasks for each specialist agent
        tasks = []
        for agent_type, task_content in subtasks.items():
            if agent_type not in agent_ports:
                self.logger.warning(f"No port found for agent type: {agent_type}")
                continue
                
            port = agent_ports[agent_type]
            url = f"http://localhost:{port}/a2a/task"
            
            # Create coroutine for each agent
            task = asyncio.create_task(self._send_task_to_agent(
                url, task_content, agent_type, session_id
            ))
            tasks.append((agent_type, task))
            
        # Wait for all tasks to complete in parallel
        results = {}
        for agent_type, task in tasks:
            try:
                result = await task
                results[agent_type] = result
                self.logger.info(f"✅ Received result from {agent_type} ({len(result)} chars)")
            except Exception as e:
                self.logger.error(f"Error from {agent_type}: {str(e)}")
                results[agent_type] = f"Error: {str(e)}"

        return results

    async def _synthesize_results(self, original_task: str, results: dict) -> str:
        """Synthesize results from specialized agents into final output"""
        self.logger.info("Synthesizing results from specialized agents...")

        # Create synthesis prompt
        synthesis_prompt = f"""
Original Task: {original_task}

Results from specialized agents:
"""

        for agent_type, result in results.items():
            synthesis_prompt += f"\n{agent_type.upper()} AGENT RESULT:\n{result}\n"

        synthesis_prompt += """
Please synthesize these results into a comprehensive, well-structured final response that addresses the original task completely.
Integrate insights from all agents and provide a cohesive answer.
"""

        # Use CrewAI to synthesize
        return await self._process_with_crewai(synthesis_prompt)

    async def _process_with_crewai(self, task_content: str) -> str:
        """Process task using CrewAI framework"""
        try:
            # Import global llm
            from Multi_Agent.crew import llm
            from crewai import Agent, Task, Crew, Process

            # Create agent with updated topic
            agent = Agent(
                role=self.agent_data['role'],
                goal=self.agent_data['goal'],
                backstory=self.agent_data['backstory'],
                llm=llm,
                verbose=False,
                allow_delegation=False
            )

            # Create task for agent
            task = Task(
                description=task_content,
                expected_output="Kết quả chi tiết và hữu ích cho user",
                agent=agent
            )

            # Create crew and execute
            crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False
            )

            result = crew.kickoff()
            return str(result)

        except Exception as e:
            self.logger.error(f"Error in CrewAI processing: {str(e)}")
            return f"Xin lỗi, tôi gặp lỗi khi xử lý yêu cầu: {str(e)}"

    def _get_agent_ports(self) -> dict:
        """Get dynamic agent port mapping from database with improved error handling"""
        try:
            from database.postgresql import get_db_connection

            agent_ports = {}
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT name, port FROM agents
                        WHERE status = 'active' AND agent_type != 'execution'
                        ORDER BY port
                    """)

                    for row in cur.fetchall():
                        agent_name, port = row
                        # Map agent_type to port for delegation
                        if agent_name in ['researcher', 'writer', 'analyst']:
                            agent_ports[agent_name] = port

            # Ensure we have at least the basic agents
            if not agent_ports:
                self.logger.warning("No agents found in database, using fallback ports")
                agent_ports = {
                    'researcher': 5003,
                    'writer': 5004,
                    'analyst': 5005
                }

            self.logger.info(f"Dynamic agent ports: {agent_ports}")
            return agent_ports

        except Exception as e:
            self.logger.error(f"Failed to get agent ports from database: {e}")
            # Fallback to default ports
            fallback_ports = {
                'researcher': 5003,
                'writer': 5004,
                'analyst': 5005
            }
            self.logger.info(f"Using fallback agent ports: {fallback_ports}")
            return fallback_ports


class DynamicAgentFactory:
    """
    Factory to create dynamic A2A agents from database
    Replaces hard-coding each agent class
    """
    
    def __init__(self):
        self.logger = logging.getLogger("DynamicAgentFactory")
        self._agent_cache = {}  # Cache to avoid creating agents repeatedly
    
    def create_agent_from_db(self, agent_name: str) -> Optional[DynamicA2AAgent]:
        """
        Create A2A agent from database record
        """
        try:
            # Check cache first
            if agent_name in self._agent_cache:
                self.logger.info(f"Returning cached agent: {agent_name}")
                return self._agent_cache[agent_name]
            
            # Load agent data from database
            agent_data = self.load_agent_data(agent_name)
            if not agent_data:
                self.logger.error(f"Agent {agent_name} not found in database")
                return None
            
            # Validate agent data
            if not self._validate_agent_data(agent_data):
                self.logger.error(f"Invalid agent data for {agent_name}")
                return None
            
            # Auto-detect agent type if not provided
            if 'agent_type' not in agent_data or not agent_data['agent_type']:
                agent_data['agent_type'] = AgentTemplateManager.detect_agent_type(
                    agent_data['role'], 
                    agent_data.get('goal', '')
                )
                self.logger.info(f"Auto-detected agent type for {agent_name}: {agent_data['agent_type']}")
            
            # Create dynamic agent
            agent = DynamicA2AAgent(agent_data)
            
            # Cache agent
            self._agent_cache[agent_name] = agent
            
            self.logger.info(f"✅ Created dynamic agent: {agent_name} (type: {agent_data['agent_type']})")
            return agent
            
        except Exception as e:
            self.logger.error(f"Failed to create agent {agent_name}: {str(e)}")
            return None
    
    def load_agent_data(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """
        Load agent data from database
        """
        try:
            from database.postgresql import get_db_connection
            
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT name, role, goal, backstory, 
                               COALESCE(port, 5000) as port,
                               COALESCE(agent_type, 'general') as agent_type,
                               COALESCE(config, '{}') as config,
                               COALESCE(status, 'active') as status
                        FROM agents 
                        WHERE name = %s AND COALESCE(status, 'active') = 'active'
                    """, (agent_name,))
                    
                    row = cur.fetchone()
                    if row:
                        return {
                            'name': row[0],
                            'role': row[1], 
                            'goal': row[2],
                            'backstory': row[3],
                            'port': row[4],
                            'agent_type': row[5],
                            'config': row[6] if isinstance(row[6], dict) else {},
                            'status': row[7]
                        }
                    else:
                        return None
                        
        except Exception as e:
            self.logger.error(f"Database error loading agent {agent_name}: {str(e)}")
            return None
    
    def _validate_agent_data(self, agent_data: Dict[str, Any]) -> bool:
        """Validate agent data from database"""
        required_fields = ['name', 'role', 'goal', 'backstory']
        
        for field in required_fields:
            if field not in agent_data or not agent_data[field]:
                self.logger.error(f"Missing required field: {field}")
                return False
        
        # Validate agent type
        agent_type = agent_data.get('agent_type', 'general')
        if not AgentTemplateManager.validate_agent_type(agent_type):
            self.logger.warning(f"Invalid agent type: {agent_type}, using 'general'")
            agent_data['agent_type'] = 'general'
        
        return True
    
    def get_available_agents(self) -> List[str]:
        """Get list of all available agents in the database"""
        try:
            from database.postgresql import get_db_connection
            
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT name FROM agents 
                        WHERE COALESCE(status, 'active') = 'active'
                        ORDER BY name
                    """)
                    
                    return [row[0] for row in cur.fetchall()]
                    
        except Exception as e:
            self.logger.error(f"Database error getting available agents: {str(e)}")
            return []
    
    def clear_cache(self):
        """Clear agent cache"""
        self._agent_cache.clear()
        self.logger.info("Agent cache cleared")
    
    def reload_agent(self, agent_name: str) -> Optional[DynamicA2AAgent]:
        """Reload agent from database (clear cache and create again)"""
        if agent_name in self._agent_cache:
            del self._agent_cache[agent_name]
        
        return self.create_agent_from_db(agent_name)
