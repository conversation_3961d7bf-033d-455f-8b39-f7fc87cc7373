#!/bin/bash

# ======================================================================
#  Multi Agents System - Docker Deployment Script
#  Scalable architecture - Auto-discovery agents from database
# ======================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print banner
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                    MULTI AGENTS SYSTEM                      ║${NC}"
echo -e "${PURPLE}║                   Docker Deployment                         ║${NC}"
echo -e "${PURPLE}╠══════════════════════════════════════════════════════════════╣${NC}"
echo -e "${PURPLE}║  🐳 Dynamic Scaling Architecture                            ║${NC}"
echo -e "${PURPLE}║  🔧 Auto-discovery agents from database                    ║${NC}"
echo -e "${PURPLE}║  🌐 A2A Protocol enabled                                   ║${NC}"
echo -e "${PURPLE}║  📊 PostgreSQL database included                           ║${NC}"
echo -e "${PURPLE}║  🖥️ Streamlit web interface                               ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if docker-compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    if command -v docker-compose > /dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    elif docker compose version > /dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    else
        print_error "Docker Compose not found. Please install Docker Compose."
        exit 1
    fi
    print_success "Docker Compose found: $DOCKER_COMPOSE_CMD"
}

# Clean up existing containers
cleanup() {
    print_status "Cleaning up existing containers..."
    $DOCKER_COMPOSE_CMD down --remove-orphans > /dev/null 2>&1 || true
    
    # Remove old images if requested
    if [[ "$1" == "--clean" ]]; then
        print_status "Removing old images..."
        docker image prune -f > /dev/null 2>&1 || true
        docker rmi $(docker images -q ai-agent*) > /dev/null 2>&1 || true
    fi
    
    print_success "Cleanup completed"
}

# Build and start services
deploy() {
    print_status "Building Multi Agents System..."
    $DOCKER_COMPOSE_CMD build --no-cache
    
    print_status "Starting services..."
    $DOCKER_COMPOSE_CMD up -d
    
    print_success "Services started successfully"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    timeout=60
    while ! $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U ai_agent_user -d ai_agent_db > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "PostgreSQL failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "PostgreSQL is ready"
    
    # Wait for Multi Agents System
    print_status "Waiting for Multi Agents System..."
    timeout=120
    while ! curl -f http://localhost:5001/health > /dev/null 2>&1; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            print_error "Multi Agents System failed to start within 120 seconds"
            print_status "Checking logs..."
            $DOCKER_COMPOSE_CMD logs multi-agents-system
            exit 1
        fi
        echo -n "."
    done
    echo
    print_success "Multi Agents System is ready"
}

# Show service status
show_status() {
    echo
    print_status "Service Status:"
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                     SERVICE STATUS                          ║${NC}"
    echo -e "${CYAN}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🐳 PostgreSQL      : http://localhost:5432                 ║${NC}"
    echo -e "${CYAN}║  🔧 Execution Agent  : http://localhost:5001                 ║${NC}"
    echo -e "${CYAN}║  📋 Planner Agent    : http://localhost:5002                 ║${NC}"
    echo -e "${CYAN}║  🔬 Researcher Agent : http://localhost:5003                 ║${NC}"
    echo -e "${CYAN}║  ✍️  Writer Agent     : http://localhost:5004                 ║${NC}"
    echo -e "${CYAN}║  📊 Analyst Agent    : http://localhost:5005                 ║${NC}"
    echo -e "${CYAN}║  🖥️ Streamlit UI     : http://localhost:8501                 ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    # Show container status
    print_status "Container Status:"
    $DOCKER_COMPOSE_CMD ps
}

# Show logs
show_logs() {
    print_status "Showing Multi Agents System logs..."
    $DOCKER_COMPOSE_CMD logs -f multi-agents-system
}

# Main deployment function
main() {
    case "${1:-deploy}" in
        "deploy")
            check_docker
            check_docker_compose
            cleanup
            deploy
            wait_for_services
            show_status
            print_success "🎊 Multi Agents System deployed successfully!"
            print_status "🖥️ Access Streamlit UI: http://localhost:8501"
            print_status "📊 View logs: ./deploy.sh logs"
            ;;
        "clean")
            check_docker
            check_docker_compose
            cleanup --clean
            deploy
            wait_for_services
            show_status
            print_success "🎊 Multi Agents System deployed with clean build!"
            ;;
        "stop")
            check_docker_compose
            print_status "Stopping services..."
            $DOCKER_COMPOSE_CMD down
            print_success "Services stopped"
            ;;
        "restart")
            check_docker_compose
            print_status "Restarting services..."
            $DOCKER_COMPOSE_CMD restart
            wait_for_services
            show_status
            print_success "Services restarted"
            ;;
        "logs")
            check_docker_compose
            show_logs
            ;;
        "status")
            check_docker_compose
            show_status
            ;;
        "shell")
            check_docker_compose
            print_status "Opening shell in Multi Agents System container..."
            $DOCKER_COMPOSE_CMD exec multi-agents-system bash
            ;;
        "streamlit")
            check_docker_compose
            print_status "Opening Streamlit UI in browser..."
            xdg-open http://localhost:8501 >/dev/null 2>&1 || open http://localhost:8501 >/dev/null 2>&1 || start http://localhost:8501 >/dev/null 2>&1
            ;;
        *)
            echo "Usage: $0 {deploy|clean|stop|restart|logs|status|shell|streamlit}"
            echo
            echo "Commands:"
            echo "  deploy    - Deploy Multi Agents System (default)"
            echo "  clean     - Clean build and deploy"
            echo "  stop      - Stop all services"
            echo "  restart   - Restart all services"
            echo "  logs      - Show system logs"
            echo "  status    - Show service status"
            echo "  shell     - Open shell in container"
            echo "  streamlit - Open Streamlit UI in browser"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
