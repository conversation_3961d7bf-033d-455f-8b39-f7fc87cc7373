import os
from dotenv import load_dotenv
import psycopg2
from psycopg2.extras import RealDictCursor

# Load environment variables
load_dotenv()

def get_db_connection():
    """Get database connection using environment variables"""
    try:
        return psycopg2.connect(
            host=os.getenv('DB_HOST'),
            dbname=os.getenv('DB_NAME'), 
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'), 
            port=os.getenv('DB_PORT')
        )
    except psycopg2.Error as e:
        print(f"Unable to connect to database: {e}")
        raise

def fetch_agents_from_db():
    """Fetch all agents from database"""
    with get_db_connection() as conn:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM agents")
            return cur.fetchall()

def fetch_tasks_from_db():
    """Fetch all tasks from database"""
    with get_db_connection() as conn:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM tasks")
            return cur.fetchall()
