# Multi-Agent
## How Multi-Agent Systems Work

Multi-agent systems involve **multiple autonomous AI agents working together to achieve complex goals**. Instead of relying on a single monolithic agent, tasks are distributed among agents with **distinct capabilities and roles**, leading to improved modularity, scalability, and robustness.

The key idea is **collaboration and coordination** between these specialized agents. This allows for the tackling of problems that are too intricate for a single agent to handle effectively.

**How Agents Interact:**

In a multi-agent system, agents interact to fulfill user requests or achieve a common objective. This interaction can involve several key aspects, as highlighted by the Agent2Agent (A2A) Protocol:

*   **Capability Discovery:** Agents can advertise their capabilities, allowing other agents to identify the most suitable agent for a specific task. This is facilitated by mechanisms like the "Agent Card" in A2A, which is in JSON format.
*   **Task Management:** Communication is oriented towards task completion. A "task" object, defined by the protocol, has a lifecycle and can be completed immediately or over a longer period. The output of a task is called an "artifact".
*   **Collaboration:** Agents send messages to exchange context, replies, artifacts, or user instructions.
*   **User Experience Negotiation:** Messages can include "parts" with specified content types, enabling agents to negotiate the appropriate format for interaction, including UI capabilities.

The OpenAI Agents SDK also emphasizes the concept of **handoffs**, where agents can delegate specific tasks to other agents.

**Common Patterns and Structures in Multi-Agent Systems**

While the sources do not explicitly list six fixed "structures," we can identify several common patterns in how multi-agent systems are organized and function:

1.  **Manager-Worker Hierarchy:** This is a common pattern where a **central "manager" or "orchestrator" agent** is responsible for breaking down complex tasks into smaller sub-tasks and delegating them to specialized "worker" agents. The manager then collects the results and synthesizes the final output. Examples include a manager agent coordinating a code interpreter and a web search agent, or a multi-agent RAG system with an orchestrator managing web, retriever, and image generation agents.

2.  **Sequential Processing:** Agents can work in a sequence, where the output of one agent becomes the input for the next. This is supported by ADK's **SequentialAgent**. This structure is suitable for workflows with a defined order of operations.
3.  **Parallel Processing:** Multiple agents can work on different parts of a task concurrently. ADK's **ParallelAgent** enables this, which can significantly reduce the overall processing time for tasks that can be divided into independent sub-tasks.

4.  **Looped Interaction:** Agents can engage in iterative processes, where they interact and refine their outputs over multiple steps until a satisfactory solution is reached. ADK's **LoopAgent** supports this type of structure.

5.  **Peer-to-Peer Collaboration:** Agents with complementary capabilities can directly communicate and collaborate with each other without a central manager for certain tasks. The A2A protocol is designed to facilitate this kind of dynamic interaction between agents built by different vendors or frameworks.

6.  **Specialized Agent Teams:** Teams of agents can be formed, each specializing in a particular domain or skill. For instance, in the Batman filming locations example, there's a web search agent for finding information and a manager agent with plotting capabilities to create the final map.

It's important to note that these patterns are not mutually exclusive, and a complex multi-agent system can incorporate elements from several of these structures. For example, a manager agent might delegate tasks to teams of specialized agents that work in parallel.

**Tools and Frameworks for Building Multi-Agent Systems**

The sources highlight several tools and frameworks for building multi-agent systems:

*   **Google Agent Development Kit (ADK):** An open-source Python toolkit for building, evaluating, and deploying intelligent AI agents, supporting single-agent and multi-agent orchestration. ADK provides different core agent categories like LLM Agents, Workflow Agents (SequentialAgent, ParallelAgent, LoopAgent), and Custom Agents. It also offers **MCPToolset** to bridge with MCP servers for tool usage.
*   **Agent2Agent (A2A) Protocol:** An open protocol for enabling AI agents to communicate, securely exchange information, and coordinate actions across different platforms and frameworks.
*   **OpenAI Agents SDK:** A lightweight SDK for building agentic AI applications with primitives like Agents, Handoffs, and Guardrails. It focuses on Python-first orchestration and built-in tracing.
*   **smolagents:** As mentioned in the Hugging Face Agents Course, this library allows combining different agents for tasks like code generation and web searching.


## How to apply in chatbot?

You can apply multi-agent systems in chatbots to build more sophisticated and capable conversational applications. Instead of a single monolithic chatbot, a multi-agent approach involves multiple specialized agents working together to handle complex tasks. This improves **modularity, scalability, and robustness**.

Here are some ways to apply multi-agent systems in chatbots, drawing from the sources and our previous conversation:

*   **Functional Decomposition:** You can divide the chatbot's responsibilities among different specialized agents. For example:
    *   A **Natural Language Understanding (NLU) Agent** to parse user input and understand the intent [as discussed in previous turn].
    *   A **Dialog Manager Agent** to keep track of the conversation flow and decide the next action [as discussed in previous turn].
    *   **Specialized Agents** for specific tasks, such as:
        *   A **Knowledge Retrieval Agent** to fetch information from a knowledge base [as discussed in previous turn].
        *   A **Task Execution Agent** to perform actions like booking appointments or processing orders [as discussed in previous turn].
        *   A **Web Agent** for browsing the internet.
        *   A **Code Interpreter Agent** for executing code.
        *   An **Image Generation Agent** for creating images.

*   **Workflow Orchestration:** You can design agents to collaborate in a specific workflow, often managed by an **Orchestrator Agent** or **Manager Agent** that delegates tasks and ensures coordination.

**Communication Between Agents:**

For multi-agent systems to function effectively, agents need to communicate with each other. The **Agent2Agent (A2A) protocol** is a new, open protocol designed to enable AI agents to communicate, securely exchange information, and coordinate actions across different platforms and even if they were built by different vendors or in different frameworks. A2A aims to foster a future where AI agents can seamlessly collaborate to automate complex workflows. Key aspects of A2A include **capability discovery**, **task management**, **collaboration through messages**, and **user experience negotiation**.

The **OpenAI Agents SDK** also introduces the concept of **handoffs**, which allows agents to delegate specific tasks to other agents.

**Code Examples:**

**1. Using Google Agent Development Kit (ADK):**

The following example, adapted from source, demonstrates the creation of an ADK agent that uses the Model Context Protocol (MCP) to connect to an external tool (in this case, a flight search tool):

```python
import os
from google.adk.agents.llm_agent import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk import types

async def get_tools_async():
    """Gets tools from the Flight Search MCP Server."""
    print("Attempting to connect to MCP Flight Search server...")
    server_params = StdioServerParameters(
        command="mcp-flight-search",
        args=["--connection_type", "stdio"],
        env={"SERP_API_KEY": os.getenv("SERP_API_KEY")},
    )
    tools, exit_stack = await MCPToolset.from_server(
        connection_params=server_params
    )
    print("MCP Toolset created successfully.")
    return tools, exit_stack

async def get_agent_async():
    """Creates an ADK Agent equipped with tools from the MCP Server."""
    tools, exit_stack = await get_tools_async()
    print(f"Fetched {len(tools)} tools from MCP server.")
    root_agent = LlmAgent(
        model=os.getenv("GEMINI_MODEL", "gemini-2.5-pro-preview-03-25"),
        name='flight_search_assistant',
        instruction='Help user to search for flights using available tools based on prompt. If return date not specified, use an empty string for one-way trips.',
        tools=tools,
    )
    return root_agent, exit_stack

async def async_main():
    session_service = InMemorySessionService()
    session = session_service.create_session(
        state={}, app_name='flight_search_app', user_id='user_flights'
    )
    query = "Find flights from Atlanta to Las Vegas 2025-05-05"
    print(f"User Query: '{query}'")
    content = types.Content(role='user', parts=[types.Part(text=query)])
    root_agent, exit_stack = await get_agent_async()
    runner = Runner(
        app_name='flight_search_app',
        agent=root_agent,
        session_service=session_service,
    )
    print("Running agent...")
    events_async = runner.run_async(
        session_id=session.id, user_id=session.user_id, new_message=content
    )
    final_content = None
    async for event in events_async:
        print(f"Event received: {event}")
    print("Closing MCP server connection...")
    await exit_stack.aclose()
    print("Cleanup complete.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(async_main())
```

This code snippet shows how an **`LlmAgent`** in ADK can be equipped with tools obtained from an **MCP server** using **`MCPToolset`** and **`StdioServerParameters`**. The **`Runner`** then executes the agent. This illustrates a basic form of agent utilizing external capabilities. To build a multi-agent system with ADK, you would define multiple agents with different roles and use **Workflow Agents** (like `SequentialAgent` or `ParallelAgent`) or custom logic to orchestrate their interactions.

**2. Using Hugging Face Agents Course (smolagents):**

The Hugging Face Agents Course demonstrates a multi-agent system where a **`Manager Agent`** coordinates a **`Web Search Agent`** and potentially other specialized agents. Here's a simplified example based on the concepts in source:

```python
import os
from PIL import Image
from smolagents import CodeAgent, GoogleSearchTool, HfApiModel, VisitWebpageTool
from smolagents.utils import encode_image_base64, make_image_url
from smolagents import OpenAIServerModel

# Define the model
model = HfApiModel(model_id="Qwen/Qwen2.5-Coder-32B-Instruct", provider="together")

# Create a web search agent
web_agent = CodeAgent(
    model=model,
    tools=[GoogleSearchTool(provider="serper"), VisitWebpageTool()],
    name="web_agent",
    description="Browses the web to find information",
    verbosity_level=0,
    max_steps=10,
)

# Create a manager agent that uses the web agent
manager_agent = CodeAgent(
    model=HfApiModel("deepseek-ai/DeepSeek-R1", provider="together", max_tokens=8096),
    tools=[],
    managed_agents=[web_agent],
    additional_authorized_imports=["geopandas", "plotly", "shapely", "json", "pandas", "numpy"],
    planning_interval=5,
    verbosity_level=2,
    max_steps=15,
)

task = """Find all Batman filming locations in the world. Also give me some supercar factories."""

result = manager_agent.run(task)
print(result)
```

In this example, the **`manager_agent`** is configured to use **`web_agent`** as a managed agent. When the `manager_agent` runs the given task, it can delegate the web searching parts to the `web_agent`, demonstrating how tasks can be split and handled by specialized agents in a multi-agent system.

**3. OpenAI Agents SDK (Conceptual):**

While the provided excerpts of the OpenAI Agents SDK don't include a multi-agent code example, the concept of **handoffs** indicates a way to structure multi-agent interactions. You would define multiple **`Agent`** instances with specific instructions and tools. When one agent encounters a task that another is better suited for, it can perform a **handoff**, delegating the task to the other agent. The `Runner` would then manage the execution and flow between these agents.

Applying multi-agent systems allows you to create more robust, modular, and scalable chatbots by leveraging the strengths of specialized AI agents that can collaborate to achieve complex goals. Frameworks like Google ADK and the concepts in the OpenAI Agents SDK provide tools and patterns for building such systems, and protocols like A2A aim to standardize communication and interoperability between diverse agents.

## References

- https://openai.github.io/openai-agents-python/
- https://medium.com/google-cloud/building-ai-agents-with-googles-agent-development-kit-adk-as-mcp-client-a-deep-dive-full-54d683713afe
- https://developers.googleblog.com/en/a2a-a-new-era-of-agent-interoperability/
- https://medium.com/@vipra_singh/ai-agents-introduction-part-1-fbec7edb857d
- https://vectorize.io/designing-agentic-ai-systems-part-1-agent-architectures/
- https://langchain-ai.github.io/langgraph/concepts/multi_agent/#handoffs-as-tools
- https://huggingface.co/learn/agents-course/en/unit2/smolagents/multi_agent_systems
