@echo off
setlocal enabledelayedexpansion

REM ======================================================================
REM  Multi Agents System - Docker Deployment Script (Windows)
REM  Scalable architecture - Auto-discovery agents from database
REM ======================================================================

title Multi Agents System - Docker Deployment

echo.
echo ================================================================
echo                    MULTI AGENTS SYSTEM
echo                   Docker Deployment
echo ================================================================
echo   Dynamic Scaling Architecture
echo   Auto-discovery agents from database
echo   A2A Protocol enabled
echo   PostgreSQL database included
echo   Streamlit web interface
echo ================================================================
echo.

REM Check if Docker is running
echo [INFO] Checking Docker...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)
echo [SUCCESS] Docker is running

REM Check Docker Compose
echo [INFO] Checking Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker Compose not found. Please install Docker Compose.
        pause
        exit /b 1
    ) else (
        set DOCKER_COMPOSE_CMD=docker compose
    )
) else (
    set DOCKER_COMPOSE_CMD=docker-compose
)
echo [SUCCESS] Docker Compose found: !DOCKER_COMPOSE_CMD!

REM Parse command line argument
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=deploy

if "%COMMAND%"=="deploy" goto :deploy
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="stop" goto :stop
if "%COMMAND%"=="restart" goto :restart
if "%COMMAND%"=="logs" goto :logs
if "%COMMAND%"=="status" goto :status
if "%COMMAND%"=="shell" goto :shell
if "%COMMAND%"=="streamlit" goto :streamlit
goto :usage

:deploy
echo [INFO] Cleaning up existing containers...
!DOCKER_COMPOSE_CMD! down --remove-orphans >nul 2>&1

echo [INFO] Building Multi Agents System...
!DOCKER_COMPOSE_CMD! build --no-cache
if errorlevel 1 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)

echo [INFO] Starting services...
!DOCKER_COMPOSE_CMD! up -d
if errorlevel 1 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)

echo [INFO] Waiting for services to be ready...
timeout /t 10 /nobreak >nul

echo [INFO] Waiting for PostgreSQL...
:wait_postgres
!DOCKER_COMPOSE_CMD! exec -T postgres pg_isready -U ai_agent_user -d ai_agent_db >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto :wait_postgres
)
echo [SUCCESS] PostgreSQL is ready

echo [INFO] Waiting for Multi Agents System...
:wait_system
curl -f http://localhost:5001/health >nul 2>&1
if errorlevel 1 (
    timeout /t 5 /nobreak >nul
    goto :wait_system
)
echo [SUCCESS] Multi Agents System is ready

goto :show_status

:clean
echo [INFO] Cleaning up existing containers and images...
!DOCKER_COMPOSE_CMD! down --remove-orphans >nul 2>&1
docker image prune -f >nul 2>&1
goto :deploy

:stop
echo [INFO] Stopping services...
!DOCKER_COMPOSE_CMD! down
echo [SUCCESS] Services stopped
goto :end

:restart
echo [INFO] Restarting services...
!DOCKER_COMPOSE_CMD! restart
timeout /t 10 /nobreak >nul
goto :show_status

:logs
echo [INFO] Showing Multi Agents System logs...
!DOCKER_COMPOSE_CMD! logs -f multi-agents-system
goto :end

:status
goto :show_status

:shell
echo [INFO] Opening shell in Multi Agents System container...
!DOCKER_COMPOSE_CMD! exec multi-agents-system bash
goto :end

:streamlit
echo [INFO] Opening Streamlit UI in browser...
start http://localhost:8501
goto :end

:show_status
echo.
echo [INFO] Service Status:
echo ================================================================
echo                     SERVICE STATUS
echo ================================================================
echo   PostgreSQL      : http://localhost:5432
echo   Execution Agent  : http://localhost:5001
echo   Planner Agent    : http://localhost:5002
echo   Researcher Agent : http://localhost:5003
echo   Writer Agent     : http://localhost:5004
echo   Analyst Agent    : http://localhost:5005
echo   Streamlit UI     : http://localhost:8501
echo ================================================================
echo.

echo [INFO] Container Status:
!DOCKER_COMPOSE_CMD! ps

echo.
echo [SUCCESS] Multi Agents System deployed successfully!
echo [INFO] Access Streamlit UI: http://localhost:8501
echo [INFO] View logs: deploy.bat logs
goto :end

:usage
echo Usage: %0 {deploy^|clean^|stop^|restart^|logs^|status^|shell^|streamlit}
echo.
echo Commands:
echo   deploy    - Deploy Multi Agents System (default)
echo   clean     - Clean build and deploy
echo   stop      - Stop all services
echo   restart   - Restart all services
echo   logs      - Show system logs
echo   status    - Show service status
echo   shell     - Open shell in container
echo   streamlit - Open Streamlit UI in browser
goto :end

:end
if "%COMMAND%"=="deploy" pause
if "%COMMAND%"=="clean" pause
if "%COMMAND%"=="status" pause
