# ======================================================================
#  Docker ignore file for AI-Agent project
#  Excludes unnecessary files from Docker build context
# ======================================================================

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
backup_*.sql

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Docker
.dockerignore
Dockerfile*
docker-compose*.yml

# Scripts (not needed in container)
scripts/
Makefile

# Development tools
.pre-commit-config.yaml
.flake8
.mypy.ini
pyproject.toml
setup.cfg

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (security)
.env.local
.env.development
.env.test
.env.production

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt
ssl/

# Nginx config (handled separately)
nginx/

# Cache directories
.cache/
.npm/
.yarn/
