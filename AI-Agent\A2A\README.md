# A2A (Agent-to-Agent) Protocol Integration

## Introduction to A2A Protocol

### What is A2A Protocol?

A2A (Agent-to-Agent) Protocol is an open protocol developed and released by Google in 2025, with the purpose of creating a common standard for communication between AI agents. This protocol addresses the problem that current AI agent systems face: lack of interoperability and collaboration between agents developed by different providers and platforms.

Some important features of A2A Protocol:

- **Open and decentralized**: Allows any agent to participate, regardless of provider or environment.
- **Based on web standards**: Uses established web technologies like HTTP/HTTPS, SSE (Server-Sent Events), and JSON-RPC 2.0.
- **High security**: Integrates enterprise-grade authentication and authorization mechanisms.
- **Support for long-running tasks**: Provides mechanisms to manage long-running tasks, including status tracking and real-time updates.
- **Multimodal**: Supports various communication data formats such as text, audio, video, and structured data.

### Why A2A Protocol?

In the context of increasingly advanced AI, intelligent agents are becoming more common but often operate independently. A2A Protocol offers many benefits:

1. **Interoperability**: Agents from different providers can communicate and collaborate.
2. **Scalability**: Systems can grow by adding new agents without changing the infrastructure.
3. **Specialization**: Agents can specialize in specific domains and collaborate to solve complex problems.
4. **Consistent user experience**: Users can interact with multiple agents through a unified interface.

### Core Components of A2A Protocol

1. **Agent Card**: A "digital business card" providing information about an agent's capabilities, communication endpoints, and authentication requirements.
2. **Task**: The basic unit of work exchanged between agents, including ID, content, and status information.
3. **Message**: Encapsulates content exchanged between agents within a task.
4. **Part**: Specific parts of a Message, which can be text, file, or structured data.
5. **Artifact**: Represents the results produced by a task.

## Overview of A2A Implementation

The A2A Protocol implementation in this project follows Google's protocol specification, allowing agents to discover each other, communicate through standardized interfaces, and collaborate on complex tasks.

**Note: This is a custom architecture implementation** built specifically for this project while maintaining compatibility with Google's A2A protocol standard. Our implementation includes project-specific optimizations, extensions, and integrations with CrewAI and PostgreSQL database that go beyond the core protocol specification, tailored to meet the specific requirements of our multi-agent system.

### A2A System Architecture

The A2A system in this project is designed according to a clear layered model:

#### 1. Communication Layer
- Handles HTTP requests/responses
- Manages Server-Sent Events (SSE) for streaming
- JSON-RPC 2.0 formatting
- Handles authentication and security

#### 2. Task Management Layer
- Manages the lifecycle of tasks
- Stores task states
- Provides mechanisms to update and query tasks

#### 3. Agent Layer
- Defines agent capabilities
- Handles business logic
- Integrates with backend systems

#### 4. Routing & Orchestration Layer
- Routes tasks to appropriate agents
- Coordinates activities between multiple agents
- Manages dependencies and workflows

## Architecture Components

### Core Components

1. **A2A Types (`types.py`)**: 
   - Defines data structures required by the A2A protocol
   - Implements JSON-RPC 2.0 request/response formats
   - Contains task states, message formats, and error codes
   - Includes the AgentCard structure for agent discovery

2. **A2A Server (`server.py`)**: 
   - Handles incoming A2A requests
   - Provides standard A2A endpoints
   - Implements the agent card discovery mechanism (`/.well-known/agent.json`)
   - Routes requests to the appropriate task manager functions

3. **Task Manager (`task_manager.py`)**:
   - Manages task lifecycle (creation, updates, completion)
   - Handles task state transitions
   - Provides both in-memory and persistent task storage
   - Supports streaming responses using Server-Sent Events (SSE)

4. **Base A2A Agent (`base_a2a_agent.py`)**:
   - Abstract base class for creating A2A-compatible agents
   - Provides core agent functionality
   - Manages agent identity and capabilities
   - Integrates with CrewAI for task execution

5. **Dynamic Agent Factory (`dynamic_agent_factory.py`)**:
   - Creates agents dynamically from database records
   - Supports different agent types and templates
   - Provides intelligent task routing and delegation
   - Handles complex workflows involving multiple agents

6. **Multi-Agent Server (`multi_agent_server.py`)**:
   - Manages multiple agents running simultaneously
   - Provides agent discovery and routing
   - Handles load balancing and task distribution
   - Exposes a unified API for the entire agent system

### Protocol Implementation

The implementation follows Google's A2A protocol specification, which includes:

- **JSON-RPC 2.0** for request/response formatting
- **Task-based** communication model
- **Server-Sent Events (SSE)** for streaming responses
- **AgentCard** for agent capabilities discovery
- **Standard endpoints** for task management
- **Well-defined error codes** and handling

## Flow Diagram

```
┌─────────────┐     ┌───────────────┐     ┌───────────────────┐
│ Client      │     │ A2A Server    │     │ Task Manager      │
│ Application │────▶│ (Starlette)   │────▶│ (Memory/Database) │
└─────────────┘     └───────────────┘     └───────────────────┘
                            │                      │
                            ▼                      ▼
                    ┌───────────────┐     ┌───────────────┐
                    │ Agent Factory │     │ Dynamic A2A   │
                    │ (DB Agents)   │◀───▶│ Agents        │
                    └───────────────┘     └───────────────┘
                            │                      │
                            ▼                      ▼
                    ┌───────────────┐     ┌───────────────┐
                    │ CrewAI        │     │ LLM           │
                    │ Integration   │────▶│ Services      │
                    └───────────────┘     └───────────────┘
```

## Detailed Workflows

### 1. Basic Task Processing Workflow

1. **Client Sends Request**:
   - Client creates a JSON-RPC request with the `tasks/send` method
   - Request contains task content and metadata information

2. **Server Receives and Processes Request**:
   - A2A Server authenticates and parses the request
   - Forwards the task to Task Manager for processing

3. **Task Manager Creates and Stores Task**:
   - Creates a new task with a unique ID
   - Updates task status to "submitted"
   - Stores the task in memory or database

4. **Task is Sent to Agent**:
   - Task Manager forwards the task to the appropriate agent
   - Agent processes the task according to business logic

5. **Returns Result**:
   - Agent updates task status to "completed"
   - Result is returned to the client as a JSON-RPC response

### 2. Task Streaming Workflow

1. **Client Sends Streaming Request**:
   - Client creates a JSON-RPC request with the `tasks/sendSubscribe` method
   - Request includes task and streaming information

2. **Server Sets Up SSE Connection**:
   - A2A Server establishes a Server-Sent Events connection
   - Task Manager creates a new task and prepares for streaming

3. **Agent Processes Task and Streams Results**:
   - Agent processes the task and creates partial results
   - Task Manager streams each part of the result to the client
   - Each update is sent as an SSE event

4. **End Streaming**:
   - When the task is completed, a final event is sent
   - The SSE connection is closed

### 3. Multi-Agent Collaboration Workflow

1. **Client Sends Complex Task**:
   - Task requires collaboration of multiple specialized agents

2. **Gateway Agent Receives Task**:
   - Gateway Agent analyzes and divides the task into sub-tasks

3. **Distribute Sub-tasks**:
   - Each sub-task is sent to the corresponding specialized agent
   - Each agent processes their part of the work

4. **Synthesize Results**:
   - Gateway Agent collects results from all agents
   - Synthesizes into a final result

5. **Return Result to Client**:
   - Synthesized result is returned to the client

## Usage Examples

### Setting Up an A2A Server

```python
from A2A.agents.types import AgentCard, AgentCapabilities, AgentSkill
from A2A.server.server import A2AServer
from A2A.server.task_manager import InMemoryTaskManager

# Create an agent card
agent_card = AgentCard(
    name="My A2A Agent",
    description="An example A2A agent",
    url="http://localhost:5000",
    version="1.0.0",
    capabilities=AgentCapabilities(
        streaming=True,
        pushNotifications=False,
        stateTransitionHistory=True
    ),
    skills=[
        AgentSkill(
            id="skill_1",
            name="Example Skill",
            description="An example skill",
            inputModes=["text"],
            outputModes=["text"]
        )
    ]
)

# Create task manager
task_manager = InMemoryTaskManager()

# Create and start server
server = A2AServer(
    host="0.0.0.0",
    port=5000,
    agent_card=agent_card,
    task_manager=task_manager
)

server.start()
```

### Creating a Custom A2A Agent

```python
from A2A.agents.base_a2a_agent import BaseA2AAgent
from A2A.agents.types import AgentSkill
from typing import List

class MyCustomAgent(BaseA2AAgent):
    def get_agent_description(self) -> str:
        return "My custom A2A agent"
    
    def get_agent_skills(self) -> List[AgentSkill]:
        return [
            AgentSkill(
                id="skill_1",
                name="Example Skill",
                description="An example skill",
                inputModes=["text"],
                outputModes=["text"]
            )
        ]
    
    async def process_task(self, task_content: str, session_id: str = None) -> str:
        # Process task and return result
        return f"Processed task: {task_content}"
```

### Using the Multi-Agent Server

```python
from A2A.server.multi_agent_server import MultiAgentServer
import asyncio

async def run_server():
    # Create multi-agent server
    server = MultiAgentServer()
    
    # Initialize agents from database
    await server.initialize()
    
    # Start all agent servers
    await server.start_all_servers()

# Run the server
asyncio.run(run_server())
```

### Complete Workflow Example: Processing Complex Request

Below is an example of how the system processes a complex request requiring collaboration of multiple agents:

```python
# 1. Client sends request
client_request = {
    "jsonrpc": "2.0",
    "id": "req-123",
    "method": "tasks/send",
    "params": {
        "id": "task-abc",
        "message": {
            "role": "user",
            "parts": [
                {
                    "type": "text",
                    "text": "Create a business plan for an AI startup, analyze the market and provide financial forecast for 3 years"
                }
            ]
        }
    }
}

# 2. Gateway Agent receives and analyzes request
# Divides into 3 sub-tasks
subtasks = {
    "business_plan": "Create a comprehensive business plan for an AI startup",
    "market_analysis": "Analyze the current AI market and trends",
    "financial_forecast": "Provide detailed financial forecast for 3 years"
}

# 3. Specialized agents process each sub-task
results = {}

# Business Plan Agent processes
results["business_plan"] = await business_plan_agent.process_task(subtasks["business_plan"])

# Market Analysis Agent processes
results["market_analysis"] = await market_analysis_agent.process_task(subtasks["market_analysis"])

# Financial Agent processes
results["financial_forecast"] = await financial_agent.process_task(subtasks["financial_forecast"])

# 4. Gateway Agent synthesizes results
final_result = await gateway_agent.synthesize_results(subtasks, results)

# 5. Return result to client
response = {
    "jsonrpc": "2.0",
    "id": "req-123",
    "result": {
        "id": "task-abc",
        "status": {
            "state": "completed"
        },
        "artifacts": [
            {
                "parts": [
                    {
                        "type": "text",
                        "text": final_result
                    }
                ]
            }
        ]
    }
}
```

## Integration with CrewAI

This A2A implementation integrates with CrewAI to leverage its agent capabilities:

1. A2A agents wrap CrewAI agents to provide A2A protocol compatibility
2. Tasks received via A2A are processed by CrewAI agents
3. Complex tasks can be broken down and delegated to specialized agents
4. Results are returned in A2A-compliant format

Integration example:

```python
from crewai import Agent as CrewAIAgent
from A2A.agents.base_a2a_agent import BaseA2AAgent

# Create CrewAI agent
crewai_agent = CrewAIAgent(
    role="Data Analyst",
    goal="Analyze data and provide insights",
    backstory="Experienced data analyst with expertise in market research",
    verbose=True
)

# Create A2A wrapper for CrewAI agent
class DataAnalystA2AAgent(BaseA2AAgent):
    def __init__(self):
        super().__init__("data_analyst", crewai_agent)
    
    def get_agent_description(self) -> str:
        return "Data analysis agent for market research and business insights"
    
    # Other methods...

# Use agent in A2A server
data_analyst_agent = DataAnalystA2AAgent()
```

## Database Integration

The system supports loading agents dynamically from a database:

1. Agent definitions are stored in a PostgreSQL database
2. The DynamicAgentFactory loads agents on demand
3. Agent skills and capabilities are derived from database records
4. New agents can be added without code changes

Database schema for agents:

```sql
CREATE TABLE agents (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    agent_type VARCHAR(50) NOT NULL,
    role TEXT NOT NULL,
    goal TEXT NOT NULL,
    backstory TEXT,
    port INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Considerations

- All communication uses HTTPS (recommended for production)
- Authentication can be implemented using standard mechanisms
- Agent Cards expose only necessary information
- Task IDs are generated with secure random UUIDs
- Role-based authorization can be implemented
- Encryption of sensitive data during transmission

## Comparison with Other Protocols

| Protocol | Focus | Advantages | Limitations |
|-----------|-----------|---------|---------|
| **A2A Protocol** | Communication between agents | Open standard, web-based, supports multiple modalities | New and evolving |
| **MCP (Model Context Protocol)** | Connecting LLMs with tools | Standardized tool and data access for LLMs | Focuses on a single agent |
| **OpenAPI** | Defining REST APIs | Widely adopted, well-supported | Not focused on AI agents |
| **gRPC** | High-performance RPC | Performance, binary encoding | More complex to implement |

## Future Development Directions

- **Improved Authentication**: Implement OAuth 2.0 and JWT for stronger authentication
- **Persistent Storage**: Store tasks and results in distributed databases
- **Scalability**: Improve performance and scalability for thousands of agents
- **Agent Marketplace**: Build a system for agent registration and discovery
- **Monitoring and Analytics**: Add performance monitoring and task analytics

## References

- [Google A2A Protocol Documentation](https://developers.googleblog.com/en/a2a-a-new-era-of-agent-interoperability/)
- [JSON-RPC 2.0 Specification](https://www.jsonrpc.org/specification)
- [Server-Sent Events (SSE)](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)
- [CrewAI Documentation](https://github.com/crewai/crewai)
- [OAuth 2.0](https://oauth.net/2/) 