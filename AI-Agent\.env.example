# ======================================================================
#  AI-Agent Environment Configuration
#  Copy this file to .env and fill in your values
# ======================================================================

# ─────────── Database Configuration ────────────────────────────────────
DB_HOST=localhost
DB_NAME=ai_agent_db
DB_USER=ai_agent_user
DB_PASSWORD=ai_agent_password
DB_PORT=5432

# ─────────── LLM API Keys ──────────────────────────────────────────────
# OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Google AI
GOOGLE_API_KEY=your_google_api_key_here

# xAI Grok
XAI_API_KEY=your_xai_api_key_here
GROK_MODEL=grok-beta
GROK_BASE_URL=https://api.x.ai/v1
GROK_API_KEY=your_grok_api_key_here

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ─────────── A2A Configuration ─────────────────────────────────────────
A2A_EXECUTION_URL=http://localhost:8001
A2A_PLANNER_URL=http://localhost:8002

# ─────────── Service Configuration ────────────────────────────────────
# CrewAI Service
CREWAI_HOST=0.0.0.0
CREWAI_PORT=8000

# A2A Agents
AGENT_HOST=0.0.0.0
# AGENT_PORT will be set by docker-compose for each agent
# AGENT_TYPE will be set by docker-compose for each agent

# ─────────── Logging Configuration ────────────────────────────────────
LOG_LEVEL=INFO
LOG_FORMAT=json

# ─────────── Security Configuration ───────────────────────────────────
# JWT Secret for session management
JWT_SECRET=your_jwt_secret_here_change_this_in_production

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# ─────────── Development Configuration ────────────────────────────────
DEBUG=false
RELOAD=false

# ─────────── Docker Configuration ─────────────────────────────────────
# These are set automatically by docker-compose
# POSTGRES_DB=ai_agent_db
# POSTGRES_USER=ai_agent_user
# POSTGRES_PASSWORD=ai_agent_password
