# AI-Agent

## Tasks

### Research Multi-Agent
+ How it works?
+ How to apply in chatbot?
+ References
  + https://openai.github.io/openai-agents-python/
  + https://medium.com/google-cloud/building-ai-agents-with-googles-agent-development-kit-adk-as-mcp-client-a-deep-dive-full-54d683713afe
  + https://developers.googleblog.com/en/a2a-a-new-era-of-agent-interoperability/
  + https://medium.com/@vipra_singh/ai-agents-introduction-part-1-fbec7edb857d
  + https://vectorize.io/designing-agentic-ai-systems-part-1-agent-architectures/
  

### Research MCP Server
+ How it works?
+ How to apply in chatbot?
+ References:
  + https://modelcontextprotocol.io/quickstart/server  
  + https://medium.com/google-cloud/model-context-protocol-mcp-with-google-gemini-llm-a-deep-dive-full-code-ea16e3fac9a3
  + https://medium.com/google-cloud/building-ai-agents-with-googles-agent-development-kit-adk-as-mcp-client-a-deep-dive-full-54d683713afe
