from crewai import Agent, Crew, Process, Task, LLM
from typing import List, Dict
from dotenv import load_dotenv
import logging
import asyncio
from functools import wraps
import os

from database.postgresql import fetch_agents_from_db, fetch_tasks_from_db

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable CrewAI telemetry
os.environ["CREWAI_DISABLE_TELEMETRY"] = "true"

def timeout_handler(timeout_seconds=120):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.error(f"Operation timed out after {timeout_seconds} seconds")
                raise TimeoutError(f"Operation took too long (> {timeout_seconds}s). Please try again.")
        return wrapper
    return decorator

def get_available_llm():
    llm_configs = [
        {
            "name": "OpenAI GPT-4o",
            "model": "gpt-4o",
            "api_key_env": "OPENAI_API_KEY",
            "temperature": 0.5
        },
        {
            "name": "xAI Grok 3",
            "model": os.getenv("GROK_MODEL", "grok-beta"),
            "api_key_env": "XAI_API_KEY",
            "temperature": 0.7
        },
        {
            "name": "OpenAI GPT-4o-mini",
            "model": "gpt-4o-mini",
            "api_key_env": "OPENAI_API_KEY",
            "temperature": 0.7
        },
        {
            "name": "Google Gemini",
            "model": "gemini-1.5-flash",
            "api_key_env": "GOOGLE_API_KEY",
            "temperature": 0.7
        }
    ]

    logger.info("🔍 Checking available LLM providers...")
    
    for config in llm_configs:
        try:
            api_key = os.getenv(config["api_key_env"])
            if api_key:
                logger.info(f"⚡ Trying {config['name']}...")
                llm = LLM(
                    model=config["model"],
                    temperature=config["temperature"],
                    api_key=api_key
                )
                logger.info(f"✅ Using {config['name']}: {config['model']}")
                return llm
            else:
                logger.info(f"⚠️  {config['name']} API key not found in environment")
        except Exception as e:
            logger.error(f"❌ {config['name']} failed: {str(e)[:100]}...")
            continue
    
    # Fallback to basic configuration if all else fails
    logger.warning("⚠️  Using fallback LLM configuration...")
    return LLM(
        model="gpt-4o",
        temperature=0.5,
        api_key=os.getenv("OPENAI_API_KEY", "")
    )
llm = get_available_llm()

class MultiAgent:
    def __init__(self, topic="AI Agent System"):
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        self.topic = topic


        # Fetch data from database
        try:
            self.logger.info("Fetching data from database...")
            agents = fetch_agents_from_db()
            tasks = fetch_tasks_from_db()
            self.logger.info(f"Fetched agents: {agents}")
            self.logger.info(f"Fetched tasks: {tasks}")

            if not agents or not tasks:
                raise ValueError("No agents or tasks found in database")

            # Replace {topic} placeholder in all agent and task data
            self.agents_data = {}
            for agent in agents:
                agent_data = {}
                for key, value in agent.items():
                    if isinstance(value, str):
                        agent_data[key] = value.replace('{topic}', self.topic)
                    else:
                        agent_data[key] = value
                self.agents_data[agent['name']] = agent_data

            self.tasks_data = {}
            for task in tasks:
                task_data = {}
                for key, value in task.items():
                    if isinstance(value, str):
                        task_data[key] = value.replace('{topic}', self.topic)
                    else:
                        task_data[key] = value
                self.tasks_data[task['name']] = task_data

            self.logger.info(f"Loaded agents: {list(self.agents_data.keys())}")
            self.logger.info(f"Loaded tasks: {list(self.tasks_data.keys())}")
            self.logger.info("Successfully initialized MultiAgent with all required components")

        except Exception as e:
            self.logger.error(f"Error initializing MultiAgent: {str(e)}")
            raise

    def update_topic(self, new_topic: str):
        """Update topic and refresh agent/task data"""
        self.topic = new_topic

        # Re-process agents data with new topic
        agents = fetch_agents_from_db()
        self.agents_data = {}
        for agent in agents:
            agent_data = {}
            for key, value in agent.items():
                if isinstance(value, str):
                    agent_data[key] = value.replace('{topic}', self.topic)
                else:
                    agent_data[key] = value
            self.agents_data[agent['name']] = agent_data

        # Re-process tasks data with new topic
        tasks = fetch_tasks_from_db()
        self.tasks_data = {}
        for task in tasks:
            task_data = {}
            for key, value in task.items():
                if isinstance(value, str):
                    task_data[key] = value.replace('{topic}', self.topic)
                else:
                    task_data[key] = value
            self.tasks_data[task['name']] = task_data

        self.logger.info(f"Updated topic to: {new_topic}")



    def create_agents(self) -> List[Agent]:
        self.logger.info("Creating agents...")
        agents = []
        
        try:
            # Create execution agent
            self.logger.info("Creating execution agent with role: " + str(self.agents_data.get('execution', {}).get('role')))
            exec_cfg = self.agents_data['execution']
            execution = Agent(
                role=exec_cfg['role'],
                goal=exec_cfg['goal'],
                backstory=exec_cfg['backstory'],
                llm=llm
            )
            agents.append(execution)
            
            # Create planner agent
            self.logger.info("Creating planner agent with role: " + str(self.agents_data.get('planner', {}).get('role')))
            planner_cfg = self.agents_data['planner']
            planner = Agent(
                role=planner_cfg['role'],
                goal=planner_cfg['goal'],
                backstory=planner_cfg['backstory'],
                llm=llm
            )
            agents.append(planner)
            
            self.logger.info(f"Successfully created all agents. Agent count: {len(agents)}")
            return agents
            
        except Exception as e:
            self.logger.error(f"Error creating agents: {str(e)}")
            raise

    def create_tasks(self, agents: List[Agent]) -> List[Task]:
        self.logger.info("Creating tasks...")
        self.logger.info(f"Agents list received: {agents}")
        self.logger.info(f"Agents count: {len(agents)}")
        tasks = []
        
        try:
            # Create execution task
            self.logger.info("Creating execution task: execution_task")
            exec_cfg = self.tasks_data['execution_task']
            execution = Task(
                description=exec_cfg['description'],
                expected_output=exec_cfg['expected_output'],
                agent=agents[0] if len(agents) > 0 else None
            )
            tasks.append(execution)
            
            # Create planning task
            self.logger.info("Creating planning task: planning_task")
            planning_cfg = self.tasks_data['planning_task']
            planning = Task(
                description=planning_cfg['description'],
                expected_output=planning_cfg['expected_output'],
                agent=agents[1] if len(agents) > 1 else None
            )
            tasks.append(planning)
            
            self.logger.info(f"Successfully created all tasks. Task count: {len(tasks)}")
            return tasks
            
        except Exception as e:
            self.logger.error(f"Error creating tasks: {str(e)}")
            raise

    def crew(self) -> Crew:
        self.logger.info("Creating crew...")
        agents = self.create_agents()
        tasks = self.create_tasks(agents)
        
        try:
            crew = Crew(
                agents=agents,
                tasks=tasks,
                process=Process.sequential,
                verbose=True
            )
            self.logger.info("Successfully created crew with all agents and tasks")
            return crew
            
        except Exception as e:
            self.logger.error(f"Error creating crew: {str(e)}")
            raise

    @timeout_handler(60)
    async def execute_simple_task(self, user_input: str) -> str:
        """Execute a simple question or task"""
        try:
            self.logger.info(f"Processing simple task: {user_input[:100]}...")
            
            # Create a simple agent for answering the question
            simple_agent = Agent(
                role="AI Assistant",
                goal="Answer user questions helpfully and accurately",
                backstory="You are an expert AI assistant trained to provide concise and helpful information.",
                llm=llm,
                verbose=False
            )
            
            # Create task for the agent
            simple_task = Task(
                description=f"Answer the following question briefly and helpfully: {user_input}",
                expected_output="A brief, accurate and helpful answer for the user",
                agent=simple_agent
            )
            
            # Create crew with just this agent and task
            simple_crew = Crew(
                agents=[simple_agent],
                tasks=[simple_task],
                process=Process.sequential,
                verbose=False
            )
            
            # Execute the crew and return result
            result = await asyncio.to_thread(simple_crew.kickoff)
            return result
            
        except Exception as e:
            self.logger.error(f"Error in execute_simple_task: {str(e)}")
            return f"Sorry, I encountered an error processing your request. Please try again or rephrase your question."
    
    @timeout_handler(180)
    async def execute_complex_task(self, user_input: str) -> str:
        """Execute a complex, multi-agent task"""
        try:
            self.logger.info(f"Processing complex task: {user_input[:100]}...")
            
            # Update topic with user input
            self.update_topic(user_input)
            
            # Create full crew with all agents
            complex_crew = self.crew()
            
            # Execute the crew and return result
            result = await asyncio.to_thread(complex_crew.kickoff)
            return result
            
        except Exception as e:
            self.logger.error(f"Error in execute_complex_task: {str(e)}")
            return f"Sorry, I encountered an error processing your complex request. Please try again or break down your request into simpler parts."