"""
Memory utilities for interacting with MEM0 and agent server
"""

import streamlit as st
import logging
import time
import requests
from typing import Dict, Any
from datetime import datetime

# Thay thế import tương đối bằng import tuyệt đối
from MEM0.streamlit_integration import get_mem0_client, get_mem0_session_id
from MEM0.streamlit_integration import enhance_user_query, store_conversation

def extract_a2a_metadata(response_data: Dict[str, Any], user_input: str, processing_time: float) -> Dict[str, Any]:
    """
    Extract relevant metadata from A2A response for MEM0 storage
    
    Args:
        response_data: Response data from A2A agent
        user_input: Original user input
        processing_time: Time taken to process the request
        
    Returns:
        Dict containing metadata for MEM0 (technical info only, no conversation content)
    """
    metadata = {
        # Basic system information
        "source": "a2a_system",
        "timestamp": datetime.now().isoformat(),
        "processing_time": processing_time,
        
        # Agent information
        "agent": response_data.get('agent', 'unknown'),
        "status": response_data.get('status', 'unknown'),
        
        # Session information
        "session_id": get_mem0_session_id(),
        
        # Technical metrics (no conversation content)
        "input_length": len(user_input),
        "response_length": len(response_data.get('result', '')),
        "complexity": "simple" if len(user_input.split()) <= 20 else "medium" if len(user_input.split()) <= 50 else "complex",
        "conversation_length": len(st.session_state.get('messages', [])),
        "memory_enhanced": st.session_state.get('use_memory', True)
    }
    
    return metadata

def process_user_input(user_input: str, use_memory: bool = True) -> dict:
    """Process user input through the Multi Agent System"""
    try:
        start_time = time.time()
        
        # Enhance prompt with memory context if enabled
        processed_input = user_input
        if use_memory:
            processed_input = enhance_user_query(user_input)
        
        # Get MEM0 session ID to use for consistency
        mem0_session_id = get_mem0_session_id()
        
        # Send request to Execution Agent
        response = requests.post(
            "http://localhost:5001/a2a/task",
            json={
                'content': processed_input,
                'session_id': mem0_session_id
            },
            timeout=180  # 3 minutes timeout for complex tasks
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            # Only store final conversation in MEM0 if enabled
            # Don't store intermediate agent conversations
            if use_memory and 'result' in result:
                # Extract metadata from A2A response
                metadata = extract_a2a_metadata(result, user_input, processing_time)
                
                # Store conversation with metadata
                store_success = store_conversation_with_metadata(
                    user_input, 
                    result.get('result', 'No result'),
                    metadata
                )
                
                if store_success:
                    logging.info(f"Stored conversation in MEM0 for user {mem0_session_id} with metadata")
                    
                    # Flag that MEM0 status should be refreshed
                    st.session_state.refresh_mem0_status = True
                
            return {
                'success': True,
                'result': result.get('result', 'No result'),
                'agent': result.get('agent', 'unknown'),
                'status': result.get('status', 'unknown'),
                'processing_time': processing_time,
                'memory_used': use_memory
            }
        else:
            return {
                'success': False,
                'error': f"HTTP {response.status_code}: {response.text}",
                'processing_time': processing_time
            }
            
    except requests.exceptions.Timeout:
        return {
            'success': False,
            'error': "Request timeout (task took longer than 3 minutes)",
            'processing_time': 180
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Error: {str(e)}",
            'processing_time': 0
        }

def store_conversation_with_metadata(user_message: str, assistant_message: str, metadata: Dict[str, Any]) -> bool:
    """
    Store a conversation exchange in MEM0 with metadata
    
    Args:
        user_message: The user's message (will be stored in messages)
        assistant_message: The assistant's response (will be stored in messages)
        metadata: Technical metadata to associate with the conversation
        
    Returns:
        bool: True if successful, False otherwise
    """
    client = get_mem0_client()
    session_id = get_mem0_session_id()
    
    # Format as messages for MEM0 - this is where conversation content goes
    messages = [
        {"role": "user", "content": user_message},
        {"role": "assistant", "content": assistant_message}
    ]
    
    # Store in MEM0 with metadata (metadata contains only technical info)
    logging.info(f"Storing conversation with {len(messages)} messages for user {session_id}")
    logging.info(f"Metadata size: {len(str(metadata))} characters")
    
    result = client.add(
        messages, 
        user_id=session_id,
        metadata=metadata,
        categories=["conversation", "a2a_system"]
    )
    
    return result.get("success", False)

def handle_user_message(current_input: str, use_memory: bool = True):
    """Handle user message and update chat history"""
    # Verify server is running before processing
    if not st.session_state.get('server_started', False):
        st.session_state.messages.append({
            "role": "assistant",
            "content": "⚠️ Server is not running. Please retry server start."
        })
        return False
    
    # Process user input and add response
    response = process_user_input(current_input, use_memory=use_memory)

    if response['success']:
        # Add assistant message to chat history with memory flag
        st.session_state.messages.append({
            "role": "assistant",
            "content": response['result'],
            "memory_used": use_memory
        })
    else:
        # Add error message to chat history
        st.session_state.messages.append({
            "role": "assistant",
            "content": f"Error: {response['error']}"
        })
    
    return True 