services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ai-agent-postgres
    environment:
      POSTGRES_DB: ai_agent_db
      POSTGRES_USER: ai_agent_user
      POSTGRES_PASSWORD: ai_agent_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-agent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_agent_user -d ai_agent_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Multi Agents System - Dynamic Scaling
  multi-agents-system:
    build: .
    container_name: ai-agent-multi-system
    environment:
      - DB_HOST=postgres
      - DB_NAME=ai_agent_db
      - DB_USER=ai_agent_user
      - DB_PASSWORD=ai_agent_password
      - DB_PORT=5432
      # A2A Configuration
      - A2A_ENABLED=true
      - A2A_AUTO_DISCOVERY=true
      - A2A_NETWORK=ai-agent-network
    ports:
      # Dynamic port range for auto-scaling agents
      - "5001-5020:5001-5020"  # Agent port range (supports up to 20 agents)
      - "8000:8000"            # main chat interface
      - "8501:8501"            # Streamlit web interface
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ai-agent-network
    volumes:
      - ./.env:/app/.env
    restart: unless-stopped
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]

volumes:
  postgres_data:

networks:
  ai-agent-network:
    driver: bridge
