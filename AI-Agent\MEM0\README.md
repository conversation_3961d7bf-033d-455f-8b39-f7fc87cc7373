# MEM0 - Memory System

Simple integration with [mem0.ai](https://app.mem0.ai/) for conversation storage and retrieval.

## Features

- Store and retrieve conversation history
- Enhance prompts with relevant context
- Automatic fallback to in-memory storage when API is unavailable
- Streamlit integration for easy use in web apps

## Setup

1. **Get your mem0.ai API key**:
   - Register at [mem0.ai](https://app.mem0.ai/)
   - Go to your account settings
   - Generate an API key with read/write permissions
   
2. **Add to your `.env` file**:
   ```
   MEM0_API_KEY=your_api_key_here
   ```

3. **Optional configuration**:
   ```
   MEM0_TIMEOUT=10.0  # API timeout in seconds
   ```

## Usage

```python
from MEM0.mem0 import MemoryClient

# Initialize client
client = MemoryClient(api_key="your_api_key_here")  # or use environment variable

# Store conversation
messages = [
    { "role": "user", "content": "Hi, I'm <PERSON>. I'm a vegetarian and I'm allergic to nuts." },
    { "role": "assistant", "content": "Hello <PERSON>! I see that you're a vegetarian with a nut allergy." }
]
client.add(messages, user_id="alex")

# Search for relevant memories
query = "What can I cook for dinner tonight?"
results = client.search(query, user_id="alex")

# Enhance a prompt with context
enhanced_prompt = client.enhance_prompt(query, user_id="alex")
```

## API

- **add(messages, user_id)**: Store messages to memory
- **search(query, user_id, limit)**: Search for relevant memories
- **enhance_prompt(prompt, user_id, limit)**: Enhance a prompt with context
- **get_storage_status()**: Get information about storage status

## Streamlit Integration

```python
from MEM0.streamlit_integration import get_mem0_client, enhance_user_query, store_conversation, show_mem0_status

# In your Streamlit app
def main():
    # Show memory status in sidebar
    show_mem0_status()
    
    # Process user input
    user_input = st.chat_input("Ask a question")
    if user_input:
        # Enhance with context
        enhanced_input = enhance_user_query(user_input)
        
        # Process through your AI
        response = your_ai_function(enhanced_input)
        
        # Store the conversation
        store_conversation(user_input, response)
```

## Fallback Mode

MEM0 automatically falls back to in-memory storage when:

1. No API key is provided
2. API key is invalid/expired
3. API is unreachable or returns errors

In fallback mode, all data is stored in memory and will be lost when the application restarts. The status is shown in the Streamlit UI when using `show_mem0_status()`. 