"""
Sidebar components for Streamlit UI
"""

import streamlit as st
import requests
import logging
from typing import List, Dict, Any

# Thay thế import tư<PERSON><PERSON> đối bằng import tuyệt đối
from MEM0.streamlit_integration import show_mem0_status

def display_server_status():
    """Display server status in sidebar"""
    if st.session_state.server_started:
        st.success("✅ Server is running")
        
        # Check server health
        try:
            response = requests.get("http://localhost:5001/health", timeout=3)
            if response.status_code == 200:
                st.success("✅ Agents are healthy")
            else:
                st.warning("⚠️ Agents may have issues")
        except:
            st.error("❌ Cannot connect to agents")
    else:
        st.error("❌ Server is not running")
        if st.button("Retry Server Start", type="primary"):
            st.session_state.server_initialized = False
            st.rerun()

def display_memory_toggle():
    """Display memory toggle in sidebar"""
    new_memory_setting = st.toggle("Use MEM0 Memory", value=st.session_state.use_memory)
    if new_memory_setting != st.session_state.use_memory:
        st.session_state.use_memory = new_memory_setting
        # Mark mem0 status for refresh when setting changes
        st.session_state.refresh_mem0_status = True
        st.rerun()
    
    # Show memory status (only refresh when needed)
    if st.session_state.use_memory:
        # Use container to prevent rerenders
        mem0_container = st.container()
        
        # Only update when needed
        if st.session_state.refresh_mem0_status:
            with mem0_container:
                show_mem0_status()
            # Reset flag after showing status
            st.session_state.refresh_mem0_status = False

def display_example_questions(example_questions: List[str] = None):
    """Display example questions in sidebar"""
    if example_questions is None:
        example_questions = [
            "What's the weather like today?",
            "How do I create a Python web server?",
            "Give me a recipe for chocolate cake",
            "Explain quantum computing to a 10-year-old"
        ]
    
    st.subheader("Example Questions")
    
    # Using columns to display examples in a grid
    example_cols = st.columns(2)
    for i, question in enumerate(example_questions):
        col_idx = i % 2
        with example_cols[col_idx]:
            if st.button(question, key=f"ex_{i}"):
                # Update session state directly instead of rerunning
                st.session_state.messages.append({"role": "user", "content": question})
                st.session_state.last_user_input = question
                st.rerun()

def render_sidebar():
    """Render the complete sidebar"""
    st.header("Settings")
    
    # Server status
    display_server_status()
    
    # Memory toggle
    display_memory_toggle()
    
    # Example questions
    display_example_questions() 