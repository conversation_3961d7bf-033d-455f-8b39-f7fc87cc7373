#!/usr/bin/env python3
"""
Multi Agents System - Streamlit Web Interface
Web interface for interacting with the Multi Agent System
"""

import streamlit as st
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to reduce noise
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.WARNING)
logging.getLogger("litellm").setLevel(logging.WARNING)

# Import from our modules
from utils.server_utils import initialize_server
from utils.memory_utils import handle_user_message
from components.sidebar import render_sidebar
from components.chat_ui import render_chat_interface

def main():
    """Main function for Streamlit app"""
    # Setup page config
    st.set_page_config(
        page_title="Multi Agent System",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize session state
    if 'messages' not in st.session_state:
        st.session_state.messages = []

    if 'use_memory' not in st.session_state:
        st.session_state.use_memory = True
        
    if 'processing' not in st.session_state:
        st.session_state.processing = False
        
    if 'refresh_mem0_status' not in st.session_state:
        st.session_state.refresh_mem0_status = True
    
    # Automatically initialize server on app start
    initialize_server()
    
    # Render sidebar
    with st.sidebar:
        render_sidebar()
        
    # Render chat interface and get user input
    user_input = render_chat_interface()
    
    # Process the input if provided
    if user_input or ('last_user_input' in st.session_state):
        # Get the input from state or directly
        if user_input:
            current_input = user_input
            # Store it for processing
            st.session_state.last_user_input = user_input
        else:
            current_input = st.session_state.last_user_input
            # Clear it after processing
            del st.session_state.last_user_input

        # Don't process if already processing (prevents duplicate processing)
        if not st.session_state.processing:
            # Add user message to chat history if not already there
            if (
                not st.session_state.messages
                or st.session_state.messages[-1]["role"] != "user"
                or st.session_state.messages[-1]["content"] != current_input
            ):
                st.session_state.messages.append({"role": "user", "content": current_input})

            # Set processing flag
            st.session_state.processing = True

            # Force rerun to update UI with user message before processing
            st.rerun()

        # Now process the message (this runs after the rerun)
        elif st.session_state.processing:
            # Handle user message and update chat history
            handle_user_message(current_input, use_memory=st.session_state.use_memory)

            # Reset processing flag
            st.session_state.processing = False

            # Rerun one last time to show the assistant response
        st.rerun()

if __name__ == "__main__":
    main() 