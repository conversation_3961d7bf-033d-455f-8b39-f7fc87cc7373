"""
Agent Templates for Multi Agents System
Templates and configurations for dynamic agent creation
"""

from typing import Dict, List, Any
from .types import AgentCapabilities, AgentSkill


# Agent Type Templates - Definitions of capabilities and skills for each agent type
AGENT_TYPE_TEMPLATES = {
    "execution": {
        "capabilities": {
            "textGeneration": True,
            "taskExecution": True,
            "coordination": True,
            "userInteraction": True,
            "delegation": True
        },
        "skills": [
            {
                "id": "task_coordination",
                "name": "Task Coordination",
                "description": "Coordinate tasks between multiple agents",
                "tags": ["coordination", "management", "delegation"],
                "examples": [],
                "inputModes": ["text"],
                "outputModes": ["text"]
            },
            {
                "id": "user_interaction",
                "name": "User Interaction",
                "description": "Direct communication and interface with users",
                "tags": ["communication", "interface", "user_experience"],
                "examples": [],
                "inputModes": ["text"],
                "outputModes": ["text"]
            },
            {
                "id": "task_delegation",
                "name": "Task Delegation",
                "description": "Delegate complex tasks to specialized agents",
                "tags": ["delegation", "task_management", "orchestration"],
                "examples": [],
                "inputModes": ["text"],
                "outputModes": ["text"]
            }
        ],
        "default_port": 5001,
        "priority": "high"
    },
    
    "planner": {
        "capabilities": {
            "textGeneration": True,
            "taskPlanning": True,
            "strategicAnalysis": True,
            "taskBreakdown": True
        },
        "skills": [
            {
                "id": "strategic_planning",
                "name": "Strategic Planning",
                "description": "Create comprehensive strategic plans",
                "tags": ["planning", "strategy", "analysis"]
            },
            {
                "id": "task_breakdown",
                "name": "Task Breakdown",
                "description": "Break down complex tasks into manageable subtasks",
                "tags": ["decomposition", "analysis", "organization"]
            },
            {
                "id": "workflow_design",
                "name": "Workflow Design",
                "description": "Design efficient workflows and processes",
                "tags": ["workflow", "process", "optimization"]
            }
        ],
        "default_port": 5002,
        "priority": "high"
    },
    
    "researcher": {
        "capabilities": {
            "textGeneration": True,
            "informationRetrieval": True,
            "dataAnalysis": True,
            "factChecking": True
        },
        "skills": [
            {
                "id": "research",
                "name": "Research",
                "description": "Gather and analyze information from various sources",
                "tags": ["research", "investigation", "data_gathering"]
            },
            {
                "id": "data_analysis",
                "name": "Data Analysis", 
                "description": "Analyze and synthesize complex data",
                "tags": ["analysis", "synthesis", "insights"]
            },
            {
                "id": "fact_checking",
                "name": "Fact Checking",
                "description": "Verify accuracy and reliability of information",
                "tags": ["verification", "accuracy", "validation"]
            }
        ],
        "default_port": 5003,
        "priority": "medium"
    },
    
    "writer": {
        "capabilities": {
            "textGeneration": True,
            "contentCreation": True,
            "editing": True,
            "stylization": True
        },
        "skills": [
            {
                "id": "creative_writing",
                "name": "Creative Writing",
                "description": "Create engaging and creative content",
                "tags": ["writing", "creativity", "content"]
            },
            {
                "id": "technical_writing",
                "name": "Technical Writing",
                "description": "Write clear technical documentation",
                "tags": ["documentation", "technical", "clarity"]
            },
            {
                "id": "content_editing",
                "name": "Content Editing",
                "description": "Edit and improve written content",
                "tags": ["editing", "improvement", "quality"]
            }
        ],
        "default_port": 5004,
        "priority": "medium"
    },
    
    "analyst": {
        "capabilities": {
            "textGeneration": True,
            "dataAnalysis": True,
            "visualization": True,
            "reporting": True
        },
        "skills": [
            {
                "id": "data_analysis",
                "name": "Data Analysis",
                "description": "Analyze complex datasets and patterns",
                "tags": ["analysis", "patterns", "insights"]
            },
            {
                "id": "report_generation",
                "name": "Report Generation",
                "description": "Generate comprehensive analytical reports",
                "tags": ["reporting", "documentation", "insights"]
            },
            {
                "id": "trend_analysis",
                "name": "Trend Analysis",
                "description": "Identify and analyze trends in data",
                "tags": ["trends", "forecasting", "patterns"]
            }
        ],
        "default_port": 5005,
        "priority": "medium"
    },
    
    "general": {
        "capabilities": {
            "textGeneration": True,
            "taskExecution": True,
            "problemSolving": True
        },
        "skills": [
            {
                "id": "general_assistance",
                "name": "General Assistance",
                "description": "Provide general help and assistance",
                "tags": ["assistance", "help", "support"]
            },
            {
                "id": "problem_solving",
                "name": "Problem Solving",
                "description": "Solve various types of problems",
                "tags": ["problem_solving", "solutions", "troubleshooting"]
            }
        ],
        "default_port": 5000,
        "priority": "low"
    }
}


# Keyword mapping to auto-detect agent type from role description
AGENT_TYPE_KEYWORDS = {
    "execution": [
        "execution", "execute", "coordinate", "manage", "lead", "interface", 
        "user", "main", "primary", "orchestrate", "delegate", "control"
    ],
    "planner": [
        "plan", "planning", "strategy", "strategic", "analyze", "breakdown", 
        "organize", "structure", "design", "workflow", "process"
    ],
    "researcher": [
        "research", "investigate", "find", "gather", "information", "data", 
        "study", "explore", "discover", "search", "fact", "verify"
    ],
    "writer": [
        "write", "writing", "content", "create", "author", "document", 
        "article", "text", "copy", "edit", "draft", "compose"
    ],
    "analyst": [
        "analyze", "analysis", "analytical", "report", "insights", "trends", 
        "patterns", "statistics", "metrics", "evaluate", "assess"
    ]
}


# Skills keyword mapping to auto-extract skills from role/goal
SKILL_KEYWORDS = {
    "research": ["research", "investigate", "find", "gather", "information", "study"],
    "planning": ["plan", "strategy", "organize", "structure", "breakdown", "design"],
    "writing": ["write", "content", "document", "article", "text", "create"],
    "analysis": ["analyze", "evaluate", "assess", "examine", "study", "insights"],
    "coordination": ["coordinate", "manage", "delegate", "orchestrate", "lead"],
    "communication": ["communicate", "interface", "interact", "discuss", "present"],
    "problem_solving": ["solve", "solution", "troubleshoot", "fix", "resolve"],
    "data_processing": ["process", "transform", "clean", "organize", "structure"],
    "reporting": ["report", "document", "summarize", "present", "visualize"],
    "fact_checking": ["verify", "validate", "check", "confirm", "authenticate"]
}


# Default capabilities for agent types
DEFAULT_CAPABILITIES = {
    "textGeneration": True,
    "taskExecution": True,
    "multimodalInput": False,
    "multimodalOutput": False,
    "streaming": True,
    "cancellation": True,
}


class AgentTemplateManager:
    """Manager to work with agent templates"""
    
    @staticmethod
    def get_template(agent_type: str) -> Dict[str, Any]:
        """Get template for agent type"""
        return AGENT_TYPE_TEMPLATES.get(agent_type, AGENT_TYPE_TEMPLATES["general"])
    
    @staticmethod
    def detect_agent_type(role: str, goal: str = "") -> str:
        """Auto-detect agent type from role and goal description"""
        text = f"{role} {goal}".lower()
        
        # Score each agent type
        scores = {}
        for agent_type, keywords in AGENT_TYPE_KEYWORDS.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                scores[agent_type] = score
        
        # Return agent type with the highest score
        if scores:
            return max(scores, key=scores.get)
        else:
            return "general"
    
    @staticmethod
    def extract_skills_from_text(role: str, goal: str = "") -> List[str]:
        """Extract skills from role and goal text"""
        text = f"{role} {goal}".lower()
        detected_skills = []
        
        for skill, keywords in SKILL_KEYWORDS.items():
            if any(keyword in text for keyword in keywords):
                detected_skills.append(skill)
        
        return detected_skills if detected_skills else ["general_assistance"]
    
    @staticmethod
    def get_default_port(agent_type: str) -> int:
        """Get default port for agent type"""
        template = AgentTemplateManager.get_template(agent_type)
        return template.get("default_port", 5000)
    
    @staticmethod
    def get_available_agent_types() -> List[str]:
        """Get list of all available agent types"""
        return list(AGENT_TYPE_TEMPLATES.keys())
    
    @staticmethod
    def validate_agent_type(agent_type: str) -> bool:
        """Validate if agent type exists"""
        return agent_type in AGENT_TYPE_TEMPLATES
