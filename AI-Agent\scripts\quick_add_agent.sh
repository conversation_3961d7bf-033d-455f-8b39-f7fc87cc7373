#!/bin/bash
"""
Quick Add Agent Script
Adds a new agent and restarts the system automatically
"""

if [ $# -lt 4 ]; then
    echo "❌ Usage: ./quick_add_agent.sh <name> <role> <goal> <backstory> [type]"
    echo ""
    echo "Example:"
    echo "./quick_add_agent.sh translator 'Translation Agent' 'Translate content between languages' 'You are a multilingual translation expert' translator"
    exit 1
fi

NAME=$1
ROLE=$2
GOAL=$3
BACKSTORY=$4
TYPE=${5:-general}

echo "🚀 Adding new agent: $NAME"

# Add agent to database
python scripts/add_agent.py add "$NAME" "$ROLE" "$GOAL" "$BACKSTORY" "$TYPE"

if [ $? -eq 0 ]; then
    echo ""
    echo "🔄 Restarting system to load new agent..."
    
    # Restart system
    docker-compose restart multi-agents-system
    
    echo ""
    echo "✅ Agent '$NAME' added and system restarted!"
    echo "🌐 The new agent should be available shortly"
    
    # Show updated agent list
    echo ""
    echo "📋 Updated agent list:"
    python scripts/add_agent.py list
else
    echo "❌ Failed to add agent. System not restarted."
fi
